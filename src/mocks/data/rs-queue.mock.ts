import { WorkStatusEnum } from 'src/utils/enum'

// RS Queue Group mock data (similar to worklist group)
export const mockRsQueueGroups: RsQueueGroupType[] = [
  {
    id: 1,
    patient_id: 'P001',
    patient_name: '<PERSON>',
    study_date: '2024-01-15',
    study_description: 'CT Chest with <PERSON>tras<PERSON>',
    status: WorkStatusEnum.PROCESSING,
    progress: 0.75,
    total_rs_count: 5,
    completed_rs_count: 3,
    last_modified: '2024-01-15 14:30:00',
  },
  {
    id: 2,
    patient_id: 'P002',
    patient_name: '<PERSON>',
    study_date: '2024-01-14',
    study_description: 'MR Brain',
    status: WorkStatusEnum.PENDING,
    progress: 0,
    total_rs_count: 3,
    completed_rs_count: 0,
    last_modified: '2024-01-14 16:45:00',
  },
  {
    id: 3,
    patient_id: 'P003',
    patient_name: '<PERSON>',
    study_date: '2024-01-13',
    study_description: 'CT Abdomen',
    status: WorkStatusEnum.SUSPENDED,
    progress: 0.33,
    total_rs_count: 6,
    completed_rs_count: 2,
    last_modified: '2024-01-13 10:20:00',
  },
  {
    id: 4,
    patient_id: 'P004',
    patient_name: '<PERSON> <PERSON>',
    study_date: '2024-01-12',
    study_description: 'MR Spine',
    status: WorkStatusEnum.SUCCEEDED,
    progress: 1,
    total_rs_count: 4,
    completed_rs_count: 4,
    last_modified: '2024-01-12 18:15:00',
  },
  {
    id: 5,
    patient_id: 'P005',
    patient_name: 'Charlie Wilson',
    study_date: '2024-01-11',
    study_description: 'CT Head',
    status: WorkStatusEnum.FAILED,
    progress: 0.5,
    total_rs_count: 2,
    completed_rs_count: 1,
    last_modified: '2024-01-11 12:30:00',
  },
]

// RS Queue items mock data (ImageRSList)
export const mockRsQueueItems: Record<number, ImageRSList[]> = {
  1: [
    {
      id: 101,
      rs_set_label: 'Liver Segmentation',
      series_number: 1,
      rs_set_date: '2024-01-15',
      import_time: '2024-01-15 14:30:00',
      status: WorkStatusEnum.SUCCEEDED,
    },
    {
      id: 102,
      rs_set_label: 'Lung Segmentation',
      series_number: 2,
      rs_set_date: '2024-01-15',
      import_time: '2024-01-15 14:32:00',
      status: WorkStatusEnum.SUCCEEDED,
    },
    {
      id: 103,
      rs_set_label: 'Heart Segmentation',
      series_number: 3,
      rs_set_date: '2024-01-15',
      import_time: '2024-01-15 14:35:00',
      status: WorkStatusEnum.PROCESSING,
    },
    {
      id: 104,
      rs_set_label: 'Kidney Segmentation',
      series_number: 4,
      rs_set_date: '2024-01-15',
      import_time: '2024-01-15 14:38:00',
      status: WorkStatusEnum.PENDING,
    },
    {
      id: 105,
      rs_set_label: 'Spleen Segmentation',
      series_number: 5,
      rs_set_date: '2024-01-15',
      import_time: '2024-01-15 14:40:00',
      status: WorkStatusEnum.PENDING,
    },
  ],
  2: [
    {
      id: 201,
      rs_set_label: 'Brain Tumor Segmentation',
      series_number: 1,
      rs_set_date: '2024-01-14',
      import_time: '2024-01-14 16:45:00',
      status: WorkStatusEnum.PENDING,
    },
    {
      id: 202,
      rs_set_label: 'White Matter Segmentation',
      series_number: 2,
      rs_set_date: '2024-01-14',
      import_time: '2024-01-14 16:47:00',
      status: WorkStatusEnum.PENDING,
    },
    {
      id: 203,
      rs_set_label: 'Gray Matter Segmentation',
      series_number: 3,
      rs_set_date: '2024-01-14',
      import_time: '2024-01-14 16:50:00',
      status: WorkStatusEnum.PENDING,
    },
  ],
  3: [
    {
      id: 301,
      rs_set_label: 'Pancreas Segmentation',
      series_number: 1,
      rs_set_date: '2024-01-13',
      import_time: '2024-01-13 10:20:00',
      status: WorkStatusEnum.SUCCEEDED,
    },
    {
      id: 302,
      rs_set_label: 'Gallbladder Segmentation',
      series_number: 2,
      rs_set_date: '2024-01-13',
      import_time: '2024-01-13 10:22:00',
      status: WorkStatusEnum.SUCCEEDED,
    },
    {
      id: 303,
      rs_set_label: 'Stomach Segmentation',
      series_number: 3,
      rs_set_date: '2024-01-13',
      import_time: '2024-01-13 10:25:00',
      status: WorkStatusEnum.SUSPENDED,
    },
    {
      id: 304,
      rs_set_label: 'Intestine Segmentation',
      series_number: 4,
      rs_set_date: '2024-01-13',
      import_time: '2024-01-13 10:28:00',
      status: WorkStatusEnum.SUSPENDED,
    },
    {
      id: 305,
      rs_set_label: 'Bladder Segmentation',
      series_number: 5,
      rs_set_date: '2024-01-13',
      import_time: '2024-01-13 10:30:00',
      status: WorkStatusEnum.SUSPENDED,
    },
    {
      id: 306,
      rs_set_label: 'Prostate Segmentation',
      series_number: 6,
      rs_set_date: '2024-01-13',
      import_time: '2024-01-13 10:32:00',
      status: WorkStatusEnum.SUSPENDED,
    },
  ],
  4: [
    {
      id: 401,
      rs_set_label: 'Cervical Spine Segmentation',
      series_number: 1,
      rs_set_date: '2024-01-12',
      import_time: '2024-01-12 18:15:00',
      status: WorkStatusEnum.SUCCEEDED,
    },
    {
      id: 402,
      rs_set_label: 'Thoracic Spine Segmentation',
      series_number: 2,
      rs_set_date: '2024-01-12',
      import_time: '2024-01-12 18:17:00',
      status: WorkStatusEnum.SUCCEEDED,
    },
    {
      id: 403,
      rs_set_label: 'Lumbar Spine Segmentation',
      series_number: 3,
      rs_set_date: '2024-01-12',
      import_time: '2024-01-12 18:20:00',
      status: WorkStatusEnum.SUCCEEDED,
    },
    {
      id: 404,
      rs_set_label: 'Spinal Cord Segmentation',
      series_number: 4,
      rs_set_date: '2024-01-12',
      import_time: '2024-01-12 18:22:00',
      status: WorkStatusEnum.SUCCEEDED,
    },
  ],
  5: [
    {
      id: 501,
      rs_set_label: 'Brain Segmentation',
      series_number: 1,
      rs_set_date: '2024-01-11',
      import_time: '2024-01-11 12:30:00',
      status: WorkStatusEnum.SUCCEEDED,
    },
    {
      id: 502,
      rs_set_label: 'Skull Segmentation',
      series_number: 2,
      rs_set_date: '2024-01-11',
      import_time: '2024-01-11 12:32:00',
      status: WorkStatusEnum.FAILED,
    },
  ],
}

// Helper function to get RS queue items by group ID
export const getRsQueueItemsByGroupId = (groupId: number): ImageRSList[] => {
  return mockRsQueueItems[groupId] || []
}

// Helper function to filter RS queue groups by status
export const filterRsQueueGroupsByStatus = (status?: string): RsQueueGroupType[] => {
  if (!status) return mockRsQueueGroups
  return mockRsQueueGroups.filter(group => group.status === status)
}
