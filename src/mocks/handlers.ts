import { http, HttpResponse, delay } from 'msw'

import {
  mockRsQueueGroups,
  mockRsQueueItems,
  getRsQueueItemsByPatientId,
  filterRsQueueGroupsByPatientId,
} from './data/rs-queue.mock'
import { mockRsTemplates } from './data/rs-templates.mock'

// RS Templates API
const rsTemplatesMockHandler = [
  http.get('*/rs-templates', async ({ request }) => {
    const url = new URL(request.url)
    const name = url.searchParams.get('name')

    await delay(100)

    let filteredTemplates = mockRsTemplates

    if (name) {
      filteredTemplates = mockRsTemplates
        .filter((template) => template.template_name.toLowerCase().includes(name.toLowerCase()))
    }

    return HttpResponse.json({
      templates: filteredTemplates,
    })
  }),
  http.delete('*/rs-templates/:id', async ({ params }) => {
    const { id } = params

    await delay(300)

    return HttpResponse.json({
      success: true,
      message: `Template ${id} deleted successfully`,
    })
  }),
]

// RS Queue API
const rsQueueMockHandler = [
  // Get RS Queue Groups
  http.get('*/rs-queue/groups', async ({ request }) => {
    const url = new URL(request.url)
    const patientId = url.searchParams.get('patient_id')
    const page = parseInt(url.searchParams.get('page') || '1', 10)
    const pageSize = 10

    await delay(300)

    let filteredGroups = mockRsQueueGroups

    if (patientId) {
      filteredGroups = filterRsQueueGroupsByPatientId(patientId)
    }

    // Pagination
    const startIndex = (page - 1) * pageSize
    const endIndex = startIndex + pageSize
    const paginatedGroups = filteredGroups.slice(startIndex, endIndex)

    return HttpResponse.json({
      rs_queue_groups: paginatedGroups,
      pagination: {
        current_page: page,
        total_data_count: filteredGroups.length,
        page_size: pageSize,
      },
    })
  }),

  // Get RS Queue Items for a specific group (using patient_id)
  http.get('*/rs-queue/groups/:patientId/items', async ({ params }) => {
    const { patientId } = params

    await delay(200)

    const items = getRsQueueItemsByPatientId(patientId as string)

    return HttpResponse.json({
      rs_queue_items: items,
      total_count: items.length,
    })
  }),

  // Get RS Queue Item Detail
  http.get('*/rs-queue/items/:id/detail', async ({ params }) => {
    const { id } = params
    const itemId = parseInt(id as string, 10)

    await delay(200)

    // Find the item across all groups
    const result = mockRsQueueGroups.reduce<{
      item: ImageRSList | null
      group: RsQueueGroupType | null
    }>((acc, group) => {
      if (acc.item) return acc

      const items = getRsQueueItemsByPatientId(group.patient_id)
      const foundItemInGroup = items.find((rsItem: ImageRSList) => rsItem.id === itemId)

      if (foundItemInGroup) {
        return { item: foundItemInGroup, group }
      }

      return acc
    }, { item: null, group: null })

    if (!result.item || !result.group) {
      return HttpResponse.json(
        { error: 'RS Queue item not found' },
        { status: 404 },
      )
    }

    let progressPercentage = 0
    if (result.item.status === 'PROCESSING') {
      progressPercentage = 75
    } else if (result.item.status === 'SUCCEEDED') {
      progressPercentage = 100
    }

    return HttpResponse.json({
      rs_queue_item: {
        ...result.item,
        patient_id: result.group.patient_id,
        study_description: `Study for ${result.group.patient_name}`,
        series_description: `Series ${result.item.series_number}`,
        processing_details: {
          started_at: result.item.import_time,
          progress_percentage: progressPercentage,
        },
      },
    })
  }),

  // Check RS Queue Item Status (for navigation validation)
  http.get('*/rs-queue/items/:id/status', async ({ params }) => {
    const { id } = params
    const itemId = parseInt(id as string, 10)

    await delay(1000) // Simulate processing time

    // Find the item to get its status
    const foundItem = Object.keys(mockRsQueueItems)
      .map((groupId) => mockRsQueueItems[parseInt(groupId, 10)])
      .flat()
      .find((rsItem) => rsItem.id === itemId)

    if (!foundItem) {
      return HttpResponse.json(
        { error: 'RS Queue item not found' },
        { status: 404 },
      )
    }

    // Simulate different status scenarios for testing
    if (itemId === 104) {
      return HttpResponse.json(
        { error: 'No matching data found' },
        { status: 400 },
      )
    }

    if (itemId === 502) {
      return HttpResponse.json(
        { error: 'Processing failed' },
        { status: 500 },
      )
    }

    return HttpResponse.json({
      status: foundItem.status || 'SUCCEEDED',
    })
  }),

  // Update RS Queue Item Status
  http.patch('*/rs-queue/items/:id/status', async ({ params, request }) => {
    const { id } = params
    const { status } = await request.json() as { status: string }

    await delay(300)

    return HttpResponse.json({
      success: true,
      message: `RS Queue item ${id} status updated to ${status}`,
      updated_item: {
        id: parseInt(id as string, 10),
        status,
      },
    })
  }),

  // Delete RS Queue Item
  http.delete('*/rs-queue/items/:id', async ({ params }) => {
    const { id } = params

    await delay(300)

    return HttpResponse.json({
      success: true,
      message: `RS Queue item ${id} deleted successfully`,
    })
  }),

  // Process RS Queue Item
  http.post('*/rs-queue/items/:id/process', async ({ params }) => {
    const { id } = params

    await delay(500)

    return HttpResponse.json({
      success: true,
      message: `RS Queue item ${id} processing started`,
      processing_id: `proc_${id}_${Date.now()}`,
    })
  }),
]

export const handlers = [
  ...rsTemplatesMockHandler,
  ...rsQueueMockHandler,
]
