import { createSlice } from '@reduxjs/toolkit'

// eslint-disable-next-line import/no-cycle
import { api } from '../../services/api'

export interface RsTemplatesState {
  templates: RsTemplateItemType[]
  searchModeCheck: boolean
}

const initialState: RsTemplatesState = {
  templates: [],
  searchModeCheck: false,
}

export const rsTemplatesSlice = createSlice({
  name: 'rsTemplates',
  initialState,
  reducers: {
    resetRsTemplatesState: () => initialState,
    deleteRsTemplate: (state, { payload }) => {
      const { id } = payload
      state.templates = state.templates.filter((template) => template.id !== id)
    },
    setSearchModeCheck: (state, { payload }) => {
      state.searchModeCheck = payload
    },
  },
  extraReducers: (builder) => {
    builder
      .addMatcher(
        api.endpoints.getRsTemplates.matchFulfilled,
        (state, { payload }) => {
          state.templates = payload.templates
        },
      )
      .addMatcher(
        api.endpoints.getRsTemplateList.matchFulfilled,
        (state, { payload }) => {
          state.templates = payload.data || []
        },
      )
  },
})

export const {
  resetRsTemplatesState,
  deleteRsTemplate,
  setSearchModeCheck,
} = rsTemplatesSlice.actions

export default rsTemplatesSlice.reducer
