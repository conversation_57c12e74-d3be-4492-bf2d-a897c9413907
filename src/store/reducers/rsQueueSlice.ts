import { createSlice, PayloadAction } from '@reduxjs/toolkit'

// eslint-disable-next-line import/no-cycle
import { api } from 'src/services/api'

export interface RsQueueState {
  // RS Queue Groups
  rsQueueGroups: RsQueueGroupType[]
  rsQueueGroupFocus: number | undefined
  
  // RS Queue Items
  rsQueueItems: Record<number, ImageRSList[]>
  rsQueueItemFocus: number | undefined
  
  // Search and Filter
  searchParameter: {
    patientId?: string
    status?: string
    studyDateStart?: string
    studyDateEnd?: string
  }
  
  // Sorting
  sorter: {
    orderKey: string
    ascend: boolean
  }
  
  // Pagination
  pagination: {
    current: number
    total: number
    pageSize: number
  }
  
  // UI State
  selectedGroupId: number | undefined
}

const initialState: RsQueueState = {
  rsQueueGroups: [],
  rsQueueGroupFocus: undefined,
  rsQueueItems: {},
  rsQueueItemFocus: undefined,
  searchParameter: {
    patientId: undefined,
    status: undefined,
    studyDateStart: undefined,
    studyDateEnd: undefined,
  },
  sorter: {
    orderKey: '',
    ascend: false,
  },
  pagination: {
    current: 1,
    total: 0,
    pageSize: 10,
  },
  selectedGroupId: undefined,
}

export const rsQueueSlice = createSlice({
  name: 'rsQueue',
  initialState,
  reducers: {
    // Reset state
    resetRsQueueState: () => initialState,
    
    // Focus management
    setRsQueueGroupFocus: (state, action: PayloadAction<number | undefined>) => {
      state.rsQueueGroupFocus = action.payload
      if (action.payload) {
        state.selectedGroupId = action.payload
      }
    },
    
    setRsQueueItemFocus: (state, action: PayloadAction<number | undefined>) => {
      state.rsQueueItemFocus = action.payload
    },
    
    // Search parameters
    setSearchParameter: (state, action: PayloadAction<Partial<RsQueueState['searchParameter']>>) => {
      state.searchParameter = { ...state.searchParameter, ...action.payload }
    },
    
    // Sorting
    setSorter: (state, action: PayloadAction<{ orderKey: string; ascend: boolean }>) => {
      state.sorter = action.payload
    },
    
    // Pagination
    setCurrentPage: (state, action: PayloadAction<number>) => {
      state.pagination.current = action.payload
    },
    
    setPagination: (state, action: PayloadAction<Partial<RsQueueState['pagination']>>) => {
      state.pagination = { ...state.pagination, ...action.payload }
    },
    
    // RS Queue Items management
    setRsQueueItems: (state, action: PayloadAction<{ groupId: number; items: ImageRSList[] }>) => {
      const { groupId, items } = action.payload
      state.rsQueueItems[groupId] = items
    },
    
    // Update single RS queue item
    updateRsQueueItem: (state, action: PayloadAction<{ groupId: number; item: ImageRSList }>) => {
      const { groupId, item } = action.payload
      if (state.rsQueueItems[groupId]) {
        const index = state.rsQueueItems[groupId].findIndex(existingItem => existingItem.id === item.id)
        if (index !== -1) {
          state.rsQueueItems[groupId][index] = item
        }
      }
    },
    
    // Remove RS queue item
    removeRsQueueItem: (state, action: PayloadAction<{ groupId: number; itemId: number }>) => {
      const { groupId, itemId } = action.payload
      if (state.rsQueueItems[groupId]) {
        state.rsQueueItems[groupId] = state.rsQueueItems[groupId].filter(item => item.id !== itemId)
      }
    },
  },
  extraReducers: (builder) => {
    builder
      // Handle RS Queue Groups API response
      .addMatcher(
        api.endpoints.getRsQueueGroup.matchFulfilled,
        (state, { payload }) => {
          state.rsQueueGroups = payload.rs_queue_groups
          if (payload.pagination) {
            state.pagination.total = payload.pagination.total
            state.pagination.pageSize = payload.pagination.page_size
          }
        },
      )
      .addMatcher(
        api.endpoints.reGetRsQueueGroup.matchFulfilled,
        (state, { payload }) => {
          state.rsQueueGroups = payload.rs_queue_groups
          if (payload.pagination) {
            state.pagination.total = payload.pagination.total
            state.pagination.pageSize = payload.pagination.page_size
          }
        },
      )
      // Handle RS Queue Items API response
      .addMatcher(
        api.endpoints.getRsQueueItems.matchFulfilled,
        (state, { payload, meta }) => {
          const groupId = meta.arg.originalArgs.rs_queue_group_id
          state.rsQueueItems[groupId] = payload.rs_queue_items
        },
      )
      // Handle item status update
      .addMatcher(
        api.endpoints.updateRsQueueItemStatus.matchFulfilled,
        (state, { payload, meta }) => {
          const { id } = meta.arg.originalArgs
          // Find and update the item in the appropriate group
          Object.keys(state.rsQueueItems).forEach(groupIdStr => {
            const groupId = parseInt(groupIdStr, 10)
            const items = state.rsQueueItems[groupId]
            if (items) {
              const index = items.findIndex(item => item.id === id)
              if (index !== -1) {
                state.rsQueueItems[groupId][index] = payload.updated_item
              }
            }
          })
        },
      )
      // Handle item deletion
      .addMatcher(
        api.endpoints.deleteRsQueueItem.matchFulfilled,
        (state, { meta }) => {
          const { id } = meta.arg.originalArgs
          // Remove the item from all groups
          Object.keys(state.rsQueueItems).forEach(groupIdStr => {
            const groupId = parseInt(groupIdStr, 10)
            if (state.rsQueueItems[groupId]) {
              state.rsQueueItems[groupId] = state.rsQueueItems[groupId].filter(item => item.id !== id)
            }
          })
        },
      )
  },
})

export const {
  resetRsQueueState,
  setRsQueueGroupFocus,
  setRsQueueItemFocus,
  setSearchParameter,
  setSorter,
  setCurrentPage,
  setPagination,
  setRsQueueItems,
  updateRsQueueItem,
  removeRsQueueItem,
} = rsQueueSlice.actions

export default rsQueueSlice.reducer
