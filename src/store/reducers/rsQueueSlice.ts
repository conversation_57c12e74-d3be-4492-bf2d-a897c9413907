import { createSlice, PayloadAction } from '@reduxjs/toolkit'

// eslint-disable-next-line import/no-cycle
import { api } from 'src/services/api'

export interface RsQueueState {
  // RS Queue Groups
  rsQueueGroups: RsQueueGroupType[]
  rsQueueGroupFocus: string | undefined

  // RS Queue Items
  rsQueueItems: Record<number, ImageRSList[]>
  rsQueueItemFocus: number | undefined

  // Search and Filter
  searchParameter: {
    patientId?: string
  }

  // Sorting
  sorter: {
    orderKey: string
    ascend: boolean
  }

  // Pagination
  pagination: {
    current: number
    total: number
    pageSize: number
  }

}

const initialState: RsQueueState = {
  rsQueueGroups: [],
  rsQueueGroupFocus: undefined,
  rsQueueItems: {},
  rsQueueItemFocus: undefined,
  searchParameter: {
    patientId: undefined,
  },
  sorter: {
    orderKey: '',
    ascend: false,
  },
  pagination: {
    current: 1,
    total: 0,
    pageSize: 10,
  },
}

export const rsQueueSlice = createSlice({
  name: 'rsQueue',
  initialState,
  reducers: {
    // Reset state
    resetRsQueueState: () => initialState,

    // Focus management
    setRsQueueGroupFocus: (state, action: PayloadAction<string | undefined>) => {
      state.rsQueueGroupFocus = action.payload
    },

    setRsQueueItemFocus: (state, action: PayloadAction<number | undefined>) => {
      state.rsQueueItemFocus = action.payload
    },

    // Search parameters
    setSearchParameter: (state, action: PayloadAction<Partial<RsQueueState['searchParameter']>>) => {
      state.searchParameter = { ...state.searchParameter, ...action.payload }
    },

    // Sorting
    setSorter: (state, action: PayloadAction<{ orderKey: string; ascend: boolean }>) => {
      state.sorter = action.payload
    },

    // Pagination
    setCurrentPage: (state, action: PayloadAction<number>) => {
      state.pagination.current = action.payload
    },

    setPagination: (state, action: PayloadAction<Partial<RsQueueState['pagination']>>) => {
      state.pagination = { ...state.pagination, ...action.payload }
    },

    // RS Queue Items management
    setRsQueueItems: (state, action: PayloadAction<{ groupId: number; items: ImageRSList[] }>) => {
      const { groupId, items } = action.payload
      state.rsQueueItems[groupId] = items
    },

    // Update single RS queue item
    updateRsQueueItem: (state, action: PayloadAction<{ groupId: number; item: ImageRSList }>) => {
      const { groupId, item } = action.payload
      if (state.rsQueueItems[groupId]) {
        const index = state.rsQueueItems[groupId].findIndex((existingItem) => existingItem.id === item.id)
        if (index !== -1) {
          state.rsQueueItems[groupId][index] = item
        }
      }
    },

    // Remove RS queue item
    removeRsQueueItem: (state, action: PayloadAction<{ groupId: number; itemId: number }>) => {
      const { groupId, itemId } = action.payload
      if (state.rsQueueItems[groupId]) {
        state.rsQueueItems[groupId] = state.rsQueueItems[groupId].filter((item) => item.id !== itemId)
      }
    },
  },
  extraReducers: (builder) => {
    builder
      // Handle RS Queue Groups API response
      .addMatcher(
        api.endpoints.getRsQueueStudies.matchFulfilled,
        (state, { payload }) => {
          state.rsQueueGroups = payload.studies
          if (payload.pagination) {
            state.pagination.total = payload.pagination.total_data_count
            state.pagination.pageSize = payload.pagination.page_size
          }
        },
      )
  },
})

export const {
  resetRsQueueState,
  setRsQueueGroupFocus,
  setRsQueueItemFocus,
  setSearchParameter,
  setSorter,
  setCurrentPage,
  setPagination,
  setRsQueueItems,
  updateRsQueueItem,
  removeRsQueueItem,
} = rsQueueSlice.actions

export default rsQueueSlice.reducer
