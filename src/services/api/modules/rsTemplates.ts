import type { EndpointBuilder } from '@reduxjs/toolkit/query/react'

import { createApiQuery } from '../base/helpers'
import type { BaseRes } from '../type'

interface GetRsTemplatesReq {
  name?: string
}

interface GetRsTemplatesRes {
  templates: RsTemplateItemType[]
}

interface DeleteRsTemplateReq {
  id: number
}

export function createRsTemplatesEndpoints(builder: EndpointBuilder<any, any, any>) {
  return {
    // Get RS templates
    getRsTemplates: builder.query<GetRsTemplatesRes, GetRsTemplatesReq>({
      query: (params: GetRsTemplatesReq) => createApiQuery({
        url: '/rs-templates',
        method: 'GET',
        params,
      }),
    }),

    // Delete RS template
    deleteRsTemplate: builder.mutation<BaseRes, DeleteRsTemplateReq>({
      query: ({ id }: DeleteRsTemplateReq) => createApiQuery({
        url: `/rs-templates/${id}`,
        method: 'DELETE',
      }),
    }),
  }
}
