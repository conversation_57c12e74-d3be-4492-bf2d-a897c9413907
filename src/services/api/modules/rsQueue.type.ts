import type { PaginationMeta, SortParams } from '../type'

// ------------------------ Request Types ------------------------

// RS Queue Studies (Groups)
export interface GetRsQueueStudiesReq extends SortParams {
  page?: number
  patient_id?: string
  status?: string
  study_date_start?: string
  study_date_end?: string
}

// RS Queue Detail (Items)
export interface GetRsQueueDetailReq extends SortParams {
  study_uid: string
}

// RS Structure
export interface GetRsStructureReq {
  rs_id: number
}

// RS Queue Item Operations
export interface DeleteRsQueueItemReq {
  rs_id: number
}

// RS Templates
export interface GetRsTemplateListReq extends SortParams {
  page?: number
}

export interface GetRsTemplateReq {
  template_id: number
}

export interface CreateRsTemplateReq {
  name: string
  description?: string
  structures: any[]
}

export interface UpdateRsTemplateReq {
  template_id: number
  name: string
  description?: string
  structures: any[]
}

export interface DeleteRsTemplateReq {
  template_id: number
}

// ------------------------ Response Types ------------------------

// RS Queue Studies Response
export interface GetRsQueueStudiesRes {
  studies: RsQueueGroupType[]
  pagination?: PaginationMeta
}

// RS Queue Detail Response
export interface GetRsQueueDetailRes {
  rs_items: ImageRSList[]
  total_count: number
}

// RS Structure Response
export interface GetRsStructureRes {
  structures: any[]
}

// RS Queue Item Operations Response
export interface DeleteRsQueueItemRes {
  success: boolean
  message: string
}

// RS Templates Response
export interface GetRsTemplateListRes {
  templates: RsTemplateType[]
  pagination?: PaginationMeta
}

export interface GetRsTemplateRes {
  template: RsTemplateType
}

export interface CreateRsTemplateRes {
  success: boolean
  message: string
  template: RsTemplateType
}

export interface UpdateRsTemplateRes {
  success: boolean
  message: string
  template: RsTemplateType
}

export interface DeleteRsTemplateRes {
  success: boolean
  message: string
}
