import type { PaginationMeta, SortParams } from '../type'

// ------------------------ Request Types ------------------------

export interface GetRsQueueGroupReq extends SortParams {
  page?: number
  patient_id?: string
  status?: string
  study_date_start?: string
  study_date_end?: string
}

export interface GetRsQueueItemsReq extends SortParams {
  rs_queue_group_id: number
}

export interface GetRsQueueItemDetailReq {
  id: number
}

export interface UpdateRsQueueItemStatusReq {
  id: number
  status: string
}

export interface DeleteRsQueueItemReq {
  id: number
}

export interface ProcessRsQueueItemReq {
  id: number
}

// ------------------------ Response Types ------------------------

export interface GetRsQueueGroupRes {
  rs_queue_groups: RsQueueGroupType[]
  pagination?: PaginationMeta
}

export interface GetRsQueueItemsRes {
  rs_queue_items: ImageRSList[]
  total_count: number
}

export interface GetRsQueueItemDetailRes {
  rs_queue_item: ImageRSList & {
    patient_id: string
    study_description: string
    series_description: string
    processing_details?: {
      started_at?: string
      completed_at?: string
      error_message?: string
      progress_percentage?: number
    }
  }
}

export interface UpdateRsQueueItemStatusRes {
  success: boolean
  message: string
  updated_item: ImageRSList
}

export interface DeleteRsQueueItemRes {
  success: boolean
  message: string
}

export interface ProcessRsQueueItemRes {
  success: boolean
  message: string
  processing_id: string
}
