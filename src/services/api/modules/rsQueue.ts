import type { EndpointBuilder } from '@reduxjs/toolkit/query/react'

import type {
  GetRsQueueStudiesReq,
  GetRsQueueStudiesRes,
  GetRsQueueDetailReq,
  GetRsQueueDetailRes,
  GetRsStructureReq,
  GetRsStructureRes,
  DeleteRsQueueItemReq,
  DeleteRsQueueItemRes,
  GetRsTemplateListReq,
  GetRsTemplateListRes,
  GetRsTemplateReq,
  GetRsTemplateRes,
  CreateRsTemplateReq,
  CreateRsTemplateRes,
  UpdateRsTemplateReq,
  UpdateRsTemplateRes,
  DeleteRsTemplateReq,
  DeleteRsTemplateRes,
} from './rsQueue.type'
import { createApiQuery } from '../base/helpers'

export function createRsQueueEndpoints(builder: EndpointBuilder<any, any, any>) {
  return {
    // 取得 RS queue study列表
    getRsQueueStudies: builder.query<GetRsQueueStudiesRes, GetRsQueueStudiesReq>({
      query: (params: GetRsQueueStudiesReq) => createApiQuery({
        url: '/rs/queue/studies',
        method: 'GET',
        params,
      }),
    }),

    // 取得 RS queue detail
    getRsQueueDetail: builder.query<GetRsQueueDetailRes, GetRsQueueDetailReq>({
      query: ({ study_uid, ...params }: GetRsQueueDetailReq) => createApiQuery({
        url: `/rs/queue/studies/${study_uid}/rs`,
        method: 'GET',
        params,
      }),
    }),

    // 取得 RS Structure資料
    getRsStructure: builder.query<GetRsStructureRes, GetRsStructureReq>({
      query: ({ rs_id }: GetRsStructureReq) => createApiQuery({
        url: `/rs/queue/rs/${rs_id}/structures`,
        method: 'GET',
      }),
    }),

    // 刪除 RS queue detail
    deleteRsQueueItem: builder.mutation<DeleteRsQueueItemRes, DeleteRsQueueItemReq>({
      query: ({ rs_id }: DeleteRsQueueItemReq) => createApiQuery({
        url: `/rs/queue/rs/${rs_id}`,
        method: 'DELETE',
      }),
    }),

    // 取得 RS template list
    getRsTemplateList: builder.query<GetRsTemplateListRes, GetRsTemplateListReq>({
      query: (params: GetRsTemplateListReq) => createApiQuery({
        url: '/rs/templates',
        method: 'GET',
        params,
      }),
    }),

    // 取得 RS template
    getRsTemplate: builder.query<GetRsTemplateRes, GetRsTemplateReq>({
      query: ({ template_id }: GetRsTemplateReq) => createApiQuery({
        url: `/rs/templates/${template_id}`,
        method: 'GET',
      }),
    }),

    // 新增 RS template
    createRsTemplate: builder.mutation<CreateRsTemplateRes, CreateRsTemplateReq>({
      query: (params: CreateRsTemplateReq) => createApiQuery({
        url: '/rs/templates',
        method: 'POST',
        body: params,
      }),
    }),

    // 更新 RS template
    updateRsTemplate: builder.mutation<UpdateRsTemplateRes, UpdateRsTemplateReq>({
      query: ({ template_id, ...body }: UpdateRsTemplateReq) => createApiQuery({
        url: `/rs/templates/${template_id}`,
        method: 'PUT',
        body,
      }),
    }),

    // 刪除 RS template
    deleteRsTemplate: builder.mutation<DeleteRsTemplateRes, DeleteRsTemplateReq>({
      query: ({ template_id }: DeleteRsTemplateReq) => createApiQuery({
        url: `/rs/templates/${template_id}`,
        method: 'DELETE',
      }),
    }),
  }
}
