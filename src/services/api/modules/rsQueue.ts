import type { EndpointBuilder } from '@reduxjs/toolkit/query/react'

import type {
  GetRsStructureRes,
  UpdateRsTemplateReq,
} from './rs.type'
import { createApiQuery } from '../base/helpers'
import { BaseRes, ResponseData } from '../type'

export function createRsQueueEndpoints(builder: EndpointBuilder<any, any, any>) {
  return {
    // 取得 RS queue study列表
    getRsQueueStudies: builder.query<ResponseData<RsQueueGroupType[]>, void>({
      query: () => createApiQuery({
        url: '/rs/queue/studies',
        method: 'GET',
      }),
    }),

    // 取得 RS queue detail
    getRsQueueDetail: builder.query<ResponseData<ImageRSList[]>, { study_uid: string }>({
      query: ({ study_uid }) => createApiQuery({
        url: `/rs/queue/studies/${study_uid}/rs`,
        method: 'GET',
      }),
    }),

    // 取得 RS Structure資料
    getRsStructure: builder.query<GetRsStructureRes, { rs_id: number }>({
      query: ({ rs_id }) => createApiQuery({
        url: `/rs/queue/rs/${rs_id}/structures`,
        method: 'GET',
      }),
    }),

    // 刪除 RS queue detail
    deleteRsQueueItem: builder.mutation<BaseRes, { rs_id: number }>({
      query: ({ rs_id }) => createApiQuery({
        url: `/rs/queue/rs/${rs_id}`,
        method: 'DELETE',
      }),
    }),

    // 取得 RS template list
    getRsTemplateList: builder.query<ResponseData<RsTemplateItemType[]>, void>({
      query: () => createApiQuery({
        url: '/rs/templates',
        method: 'GET',
      }),
    }),

    // 取得 RS template
    getRsTemplate: builder.query<RsTemplateDetailType, { template_id: number }>({
      query: ({ template_id }) => createApiQuery({
        url: `/rs/templates/${template_id}`,
        method: 'GET',
      }),
    }),

    // 新增 RS template
    createRsTemplate: builder.mutation<BaseRes, RsTemplateDetailType>({
      query: (params: RsTemplateDetailType) => createApiQuery({
        url: '/rs/templates',
        method: 'POST',
        body: params,
      }),
    }),

    // 更新 RS template
    updateRsTemplate: builder.mutation<BaseRes, UpdateRsTemplateReq>({
      query: ({ template_id, ...body }: UpdateRsTemplateReq) => createApiQuery({
        url: `/rs/templates/${template_id}`,
        method: 'PUT',
        body,
      }),
    }),

    // 刪除 RS template
    deleteRsTemplate: builder.mutation<BaseRes, { template_id: number }>({
      query: ({ template_id }) => createApiQuery({
        url: `/rs/templates/${template_id}`,
        method: 'DELETE',
      }),
    }),
  }
}
