import type { EndpointBuilder } from '@reduxjs/toolkit/query/react'

import type {
  GetRsQueueStudiesRes,
  GetRsQueueDetailRes,
  GetRsStructureRes,
  DeleteRsQueueItemRes,
  GetRsTemplateListRes,
  GetRsTemplateRes,
  CreateRsTemplateReq,
  CreateRsTemplateRes,
  UpdateRsTemplateReq,
  UpdateRsTemplateRes,
  DeleteRsTemplateRes,
} from './rsQueue.type'
import { createApiQuery } from '../base/helpers'

export function createRsQueueEndpoints(builder: EndpointBuilder<any, any, any>) {
  return {
    // 取得 RS queue study列表
    getRsQueueStudies: builder.query<GetRsQueueStudiesRes, void>({
      query: () => createApiQuery({
        url: '/rs/queue/studies',
        method: 'GET',
      }),
    }),

    // 取得 RS queue detail
    getRsQueueDetail: builder.query<GetRsQueueDetailRes, { study_uid: string }>({
      query: ({ study_uid }) => createApiQuery({
        url: `/rs/queue/studies/${study_uid}/rs`,
        method: 'GET',
      }),
    }),

    // 取得 RS Structure資料
    getRsStructure: builder.query<GetRsStructureRes, { rs_id: number }>({
      query: ({ rs_id }) => createApiQuery({
        url: `/rs/queue/rs/${rs_id}/structures`,
        method: 'GET',
      }),
    }),

    // 刪除 RS queue detail
    deleteRsQueueItem: builder.mutation<DeleteRsQueueItemRes, { rs_id: number }>({
      query: ({ rs_id }) => createApiQuery({
        url: `/rs/queue/rs/${rs_id}`,
        method: 'DELETE',
      }),
    }),

    // 取得 RS template list
    getRsTemplateList: builder.query<GetRsTemplateListRes, void>({
      query: () => createApiQuery({
        url: '/rs/templates',
        method: 'GET',
      }),
    }),

    // 取得 RS template
    getRsTemplate: builder.query<GetRsTemplateRes, { template_id: number }>({
      query: ({ template_id }) => createApiQuery({
        url: `/rs/templates/${template_id}`,
        method: 'GET',
      }),
    }),

    // 新增 RS template
    createRsTemplate: builder.mutation<CreateRsTemplateRes, CreateRsTemplateReq>({
      query: (params: CreateRsTemplateReq) => createApiQuery({
        url: '/rs/templates',
        method: 'POST',
        body: params,
      }),
    }),

    // 更新 RS template
    updateRsTemplate: builder.mutation<UpdateRsTemplateRes, UpdateRsTemplateReq>({
      query: ({ template_id, ...body }: UpdateRsTemplateReq) => createApiQuery({
        url: `/rs/templates/${template_id}`,
        method: 'PUT',
        body,
      }),
    }),

    // 刪除 RS template
    deleteRsTemplate: builder.mutation<DeleteRsTemplateRes, { template_id: number }>({
      query: ({ template_id }) => createApiQuery({
        url: `/rs/templates/${template_id}`,
        method: 'DELETE',
      }),
    }),
  }
}
