import type { EndpointBuilder } from '@reduxjs/toolkit/query/react'

import type {
  GetRsQueueGroupReq,
  GetRsQueueGroupRes,
  GetRsQueueItemsReq,
  GetRsQueueItemsRes,
  GetRsQueueItemDetailReq,
  GetRsQueueItemDetailRes,
  UpdateRsQueueItemStatusReq,
  UpdateRsQueueItemStatusRes,
  DeleteRsQueueItemReq,
  DeleteRsQueueItemRes,
  ProcessRsQueueItemReq,
  ProcessRsQueueItemRes,
} from './rsQueue.type'
import { createApiQuery } from '../base/helpers'

export function createRsQueueEndpoints(builder: EndpointBuilder<any, any, any>) {
  return {
    // Get RS queue groups (similar to worklist groups)
    getRsQueueGroup: builder.query<GetRsQueueGroupRes, GetRsQueueGroupReq>({
      query: (params: GetRsQueueGroupReq) => createApiQuery({
        url: '/rs-queue/groups',
        method: 'GET',
        params,
      }),
    }),

    // Get RS queue groups without token refresh (for polling)
    reGetRsQueueGroup: builder.query<GetRsQueueGroupRes, GetRsQueueGroupReq>({
      query: (params: GetRsQueueGroupReq) => createApiQuery({
        url: '/rs-queue/groups',
        method: 'GET',
        params,
      }),
    }),

    // Get RS queue items for a specific group
    getRsQueueItems: builder.query<GetRsQueueItemsRes, GetRsQueueItemsReq>({
      query: ({ rs_queue_group_id, ...params }: GetRsQueueItemsReq) => createApiQuery({
        url: `/rs-queue/groups/${rs_queue_group_id}/items`,
        method: 'GET',
        params,
      }),
    }),

    // Get RS queue item detail
    getRsQueueItemDetail: builder.mutation<GetRsQueueItemDetailRes, GetRsQueueItemDetailReq>({
      query: ({ id }: GetRsQueueItemDetailReq) => createApiQuery({
        url: `/rs-queue/items/${id}/detail`,
        method: 'GET',
      }),
    }),

    // Update RS queue item status
    updateRsQueueItemStatus: builder.mutation<UpdateRsQueueItemStatusRes, UpdateRsQueueItemStatusReq>({
      query: ({ id, status }: UpdateRsQueueItemStatusReq) => createApiQuery({
        url: `/rs-queue/items/${id}/status`,
        method: 'PATCH',
        body: { status },
      }),
    }),

    // Delete RS queue item
    deleteRsQueueItem: builder.mutation<DeleteRsQueueItemRes, DeleteRsQueueItemReq>({
      query: ({ id }: DeleteRsQueueItemReq) => createApiQuery({
        url: `/rs-queue/items/${id}`,
        method: 'DELETE',
      }),
    }),

    // Process RS queue item (start processing)
    processRsQueueItem: builder.mutation<ProcessRsQueueItemRes, ProcessRsQueueItemReq>({
      query: ({ id }: ProcessRsQueueItemReq) => createApiQuery({
        url: `/rs-queue/items/${id}/process`,
        method: 'POST',
      }),
    }),

    // Check RS queue item status (for navigation validation)
    checkRsQueueItemStatus: builder.mutation<{ status: string }, { id: number }>({
      query: ({ id }) => createApiQuery({
        url: `/rs-queue/items/${id}/status`,
        method: 'GET',
      }),
    }),
  }
}
