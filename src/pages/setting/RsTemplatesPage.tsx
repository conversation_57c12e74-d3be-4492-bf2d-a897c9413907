import { useEffect } from 'react'

import { Button, Form } from 'antd'
import { Content } from 'antd/es/layout/layout'

import { Search } from 'src/components/Form'
import { Head } from 'src/components/Head'
import RsTemplatesTable from 'src/components/Table/RsTemplatesTable'
import useAlert from 'src/hooks/useAlert'
import i18n from 'src/i18n'
import { useLazyGetRsTemplatesQuery } from 'src/services/api'
import { useAppDispatch } from 'src/store/hook'
import { setSearchModeCheck } from 'src/store/reducers/rsTemplatesSlice'

import 'src/styles/pages/protocols.css'

function RsTemplatesPage() {
  const [form] = Form.useForm()

  const [trigger, { isFetching }] = useLazyGetRsTemplatesQuery()
  const dispatch = useAppDispatch()
  // hook
  const handleAlert = useAlert()

  const getRsTemplates = () => {
    const name = form.getFieldsValue().searchName
    // In search mode order can't be changed
    dispatch(setSearchModeCheck(!!name))

    try {
      trigger({ name })
    } catch (e) {
      handleAlert({ content: (e as Err).data?.detail }, 'Msg', 'error')
    }
  }

  useEffect(() => {
    getRsTemplates()
  }, [])

  return (
    <>
      <Head>{i18n.t('titles.rs_templates')}</Head>
      <Content style={{ gap: '1.5rem' }}>
        <section style={{ overflowX: 'auto' }}>
          <nav style={{
            width: '100%',
            minWidth: '673px',
            display: 'flex',
            justifyContent: 'space-between',
          }}
          >
            <Search form={form} handleSearch={getRsTemplates} />
            <Button type="primary">
              {i18n.t('buttons.add')}
            </Button>
          </nav>
        </section>
        <RsTemplatesTable spinning={isFetching} reload={getRsTemplates} />
      </Content>
    </>
  )
}

export default RsTemplatesPage
