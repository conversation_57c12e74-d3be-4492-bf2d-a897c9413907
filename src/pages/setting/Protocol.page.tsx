import { useEffect } from 'react'

import {
  <PERSON>ton, Flex, Form, Modal, type ButtonProps as AntdButtonProps,
} from 'antd'
import { Content, Footer } from 'antd/es/layout/layout'

import { Search } from 'src/components/Form'
import { Head } from 'src/components/Head'
import LeaveConfirmModal from 'src/components/Modal/LeaveConfirmModal'

import CreateNewProtocolModal from './components/CreateNewProtocolModal'
import ProtocolTable from './components/ProtocolTable'
import useAlert from 'src/hooks/useAlert'
import { useAntModal } from 'src/hooks/useAntModal'
import i18n from 'src/i18n'
import { useLazyGetProtocolQuery, usePutProtocolSortMutation } from 'src/services/api'
import { useAppSelector, useAppDispatch } from 'src/store/hook'
import { resetConfigState } from 'src/store/reducers/configSlice'
import { resetProtocolState, updateProtocolSearchMode } from 'src/store/reducers/protocolSlice'
import { resetAntModalProps, saveAntModalProps } from 'src/utils/saveButton'
import 'src/styles/pages/protocols.css'

function Protocol() {
  const [form] = Form.useForm()

  const { protocols, updateProtocolSortCheck } = useAppSelector((state) => state.protocolReducer)
  const [trigger, { isFetching }] = useLazyGetProtocolQuery()
  const [putProtocolSortMutation] = usePutProtocolSortMutation()
  const dispatch = useAppDispatch()
  // hook
  const handleAlert = useAlert()

  const getProtocol = () => {
    const name = form.getFieldsValue().searchName
    // In search mode order can't be changed
    dispatch(updateProtocolSearchMode({ bool: !!name }))

    try {
      trigger({ name })
    } catch (e) {
      handleAlert({ content: (e as Err).data?.detail }, 'Msg', 'error')
    }
  }

  const handleSave = async () => {
    if (updateProtocolSortCheck) {
      const updateProtocol = protocols.map((item) => item.id)
      try {
        await putProtocolSortMutation({ protocol_sort: updateProtocol }).unwrap()
        getProtocol()
      } catch (e) {
        handleAlert({ title: i18n.t('error_titles.save'), content: (e as Err).data?.detail }, 'Msg', 'error')
      }
    }
  }

  // modal
  const createModal = useAntModal({
    triggerProps: {
      disabled: updateProtocolSortCheck,
    },
    modalProps: {
      title: i18n.t('modal_titles.create_new_protocol'),
      okText: i18n.t('buttons.next'),
      width: '765px',
      className: 'create-new-modal',
      styles: {
        header: {
          padding: '0 30px',
        },
        body: {
          height: '70vh',
          minHeight: 600,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
        },
      },
    },
  })
  const resetModal = useAntModal({
    ...resetAntModalProps,
    modalProps: {
      ...resetAntModalProps?.modalProps,
      okText: i18n.t('buttons.ok'),
      onOk: getProtocol,
    },
  })
  const saveModal = useAntModal({
    ...saveAntModalProps,
    modalProps: {
      ...saveAntModalProps?.modalProps,
      onOk: handleSave,
    },
  })

  const { type: savebuttonsType, ...saveModalTriggerProps } = saveModal.triggerProps
  const { type: resetbuttonsType, ...resetModalTriggerProps } = resetModal.triggerProps

  useEffect(() => {
    if (protocols.length === 0) getProtocol()

    return () => {
      dispatch(resetConfigState())
      dispatch(resetProtocolState())
    }
  }, [])

  return (
    <>
      <Head>{i18n.t('titles.protocols')}</Head>
      <Content style={{ gap: '1.5rem' }}>
        <section style={{ overflowX: 'auto' }}>
          <nav style={{
            width: '100%',
            minWidth: '673px',
            display: 'flex',
            justifyContent: 'space-between',
          }}
          >
            <Search form={form} handleSearch={getProtocol} />
            <Button
              htmlType="button"
              className="gray"
              {...createModal.triggerProps}
            >
              {i18n.t('buttons.new_protocol')}
            </Button>
          </nav>
        </section>
        <ProtocolTable spinning={isFetching} reload={getProtocol} />
      </Content>
      {updateProtocolSortCheck && (
        <Footer>
          <Flex justify="end" gap={16}>
            <Button {...resetModalTriggerProps as AntdButtonProps} />
            <Button {...saveModalTriggerProps as AntdButtonProps} />
          </Flex>
        </Footer>
      )}

      <Modal {...resetModal.modalProps} />
      <Modal {...saveModal.modalProps} />
      <CreateNewProtocolModal {...createModal} />
      <LeaveConfirmModal condition={updateProtocolSortCheck} />
    </>
  )
}

export default Protocol
