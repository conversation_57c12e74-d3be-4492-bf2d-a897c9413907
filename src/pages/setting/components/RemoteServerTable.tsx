import React, { useEffect } from 'react'

import {
  Button, Col, Form, Input, List, Modal, Row,
} from 'antd'

import { LoadingIcon2, XCircleIcon } from 'src/assets/icons'
import { TableInput } from 'src/components/Form'
import { useAntModal } from 'src/hooks/useAntModal'
import i18n from 'src/i18n'
import { useAppSelector } from 'src/store/hook'
import { requiredRules, duplicatedRules } from 'src/utils/verify'

import DebouncedWithHOC from '../../../components/DebouncedWithHOC'
import FieldExtra from '../../../components/Form/FieldExtra'

import 'src/styles/components/ListLayout.css'

interface Props {
  pathname: string
  tableLoading: boolean
  handleAdd: (data: any) => void
  handleDelete: (data: RemoteServerType) => void
  handleChange: (id: number, fieldName: string, data: RemoteServerType[keyof RemoteServerType]) => void
}

type FiledName = keyof Omit<RemoteServerType, 'id'>

const getPlaceholder = (titleKey: string): string => i18n.t(
  'form_placeholders.enter_variable',
  { variable: i18n.t(`titles.${titleKey}`), joinArrays: ' ' },
)

const itemInputProps = {
  style: { flex: 3, minWidth: 200 },
}

function RemoteServerTable({
  pathname,
  tableLoading,
  handleAdd,
  handleChange,
  handleDelete,
}: Props) {
  const { remoteServer } = useAppSelector((state) => state.remoteReducer)
  const [form] = Form.useForm()

  const showModal = async () => {
    try {
      await form.validateFields()
    } catch (e) {
      throw new Error('form valideted error')
    }
  }

  // modal
  const addModal = useAntModal({
    triggerProps: {
      onClick: showModal,
      variant: 'outlined',
      color: 'primary',
      style: { marginTop: 40, height: '31px', lineHeight: 0 },
      children: i18n.t('buttons.add'),
      htmlType: 'submit',
    },
    modalProps: {
      styles: {
        body: { padding: '4.5rem 0' },
      },
      title: i18n.t('modal_titles.link_to', { value: i18n.t('titles.remote_server'), joinArrays: ' ' }),
      okText: i18n.t('buttons.link'),
      onOk: () => {
        const {
          // eslint-disable-next-line @typescript-eslint/naming-convention
          name, description, ae_title, ip, port,
        } = form.getFieldsValue()
        handleAdd({
          name, description, ae_title, ip, port,
        })
        form.resetFields()
      },
      width: 350,
      centered: true,
    },
  })

  const deleteModal = useAntModal<RemoteServerType>({
    triggerProps: {
      children: <XCircleIcon />,
      htmlType: 'button',
      className: 'ant-list-delete',
      style: {
        backgroundColor: 'transparent',
        cursor: 'pointer',
        lineHeight: '32px',
        boxShadow: 'none',
      },
    },
    modalProps: {
      render(record) {
        return i18n.t(
          'modal_contents.delete_confirmation',
          { item: `"${record?.name}"`, joinArrays: ' ' },
        )
      },
      title: i18n.t('modal_titles.delete_confirmation'),
      okText: i18n.t('buttons.delete'),
      onOk: (record) => {
        if (record) {
          handleDelete(record)
        }
      },
      width: 350,
      styles: { body: { padding: '4.75rem 0' } },
    },
  })

  const handleInputChange = (id: number, fieldName: FiledName, value: string) => {
    handleChange(id, fieldName, value)
  }

  useEffect(() => {
    form.resetFields()
  }, [remoteServer])

  return (
    <>
      <Form form={form} layout="vertical">
        <Row gutter={[16, 0]}>
          <Col style={{ flex: 3, minWidth: 200 }}>
            <Form.Item
              label={i18n.t('titles.name')}
              name="name"
              rules={[
                requiredRules(i18n.t('titles.name')),
                duplicatedRules('name', remoteServer),
              ]}
            >
              <Input placeholder={getPlaceholder('name')} />
            </Form.Item>
          </Col>
          <Col style={{ flex: 3, minWidth: 200 }}>
            <Form.Item
              label={i18n.t('titles.description')}
              name="description"
            >
              <Input placeholder={getPlaceholder('description')} />
            </Form.Item>
          </Col>
          <Col style={{ flex: 3, minWidth: 200 }}>
            <Form.Item
              label={i18n.t('titles.ae_title')}
              name="ae_title"
              rules={[
                requiredRules(i18n.t('titles.ae_title')),
                duplicatedRules('ae_title', remoteServer),
              ]}
            >
              <Input placeholder={getPlaceholder('ae_title')} />
            </Form.Item>
          </Col>
          <Col style={{ flex: 3, minWidth: 200 }}>
            <Form.Item
              label={i18n.t('titles.ip')}
              name="ip"
              rules={[
                requiredRules(i18n.t('titles.ip')),
                duplicatedRules('ip', remoteServer),
              ]}
            >
              <Input placeholder={getPlaceholder('ip')} />
            </Form.Item>
          </Col>
          {pathname === '/destination' && (
            <Col style={{ flex: 3, minWidth: 200 }}>
              <Form.Item
                label={i18n.t('titles.port')}
                name="port"
                rules={[
                  requiredRules(i18n.t('titles.port')),
                  duplicatedRules('port', remoteServer),
                ]}
              >
                <Input placeholder={getPlaceholder('port')} />
              </Form.Item>
            </Col>
          )}
          <Col style={{ width: 100 }}>
            <Button {...addModal.triggerProps} />
          </Col>
        </Row>
      </Form>

      <List
        dataSource={remoteServer}
        renderItem={(item) => {
          const isConnected = item.status === 'connected'
          return (
            <List.Item
              style={{
                backgroundColor: isConnected ? '#f6ffed' : '#fff2f0',
                border: `1px solid ${isConnected ? '#b7eb8f' : '#ffccc7'}`,
                borderRadius: 6,
                marginBottom: 8,
                padding: '12px 16px',
              }}
            >
              <Row gutter={[16, 0]} style={{ width: '100%', alignItems: 'center' }}>
                {/* Name */}
                <Col {...itemInputProps}>
                  <FieldExtra
                    label={i18n.t('titles.name')}
                    required
                    duplicated={remoteServer.filter((server) => server.id !== item.id && server.name === item.name).length > 0}
                  >
                    <DebouncedWithHOC
                      delay={500}
                      onDebouncedChange={(value: string) => handleInputChange(item.id, 'name', value)}
                    >
                      <TableInput defaultValue={item.name} />
                    </DebouncedWithHOC>
                  </FieldExtra>
                </Col>

                {/* Description */}
                <Col {...itemInputProps}>
                  <FieldExtra label={i18n.t('titles.description')}>
                    <DebouncedWithHOC
                      delay={500}
                      onDebouncedChange={(value: string) => handleInputChange(item.id, 'description', value)}
                    >
                      <TableInput defaultValue={item.description} />
                    </DebouncedWithHOC>
                  </FieldExtra>
                </Col>

                {/* AE Title */}
                <Col {...itemInputProps}>
                  <FieldExtra
                    label={i18n.t('titles.ae_title')}
                    required
                    duplicated={remoteServer.filter((server) => server.id !== item.id && server.ae_title === item.ae_title).length > 0}
                  >
                    <DebouncedWithHOC
                      delay={500}
                      onDebouncedChange={(value: string) => handleInputChange(item.id, 'ae_title', value)}
                    >
                      <TableInput defaultValue={item.ae_title} />
                    </DebouncedWithHOC>
                  </FieldExtra>
                </Col>

                {/* IP */}
                <Col {...itemInputProps}>
                  <FieldExtra
                    label={i18n.t('titles.ip')}
                    required
                    duplicated={remoteServer.filter((server) => server.id !== item.id && server.ip === item.ip).length > 0}
                  >
                    <DebouncedWithHOC
                      delay={500}
                      onDebouncedChange={(value: string) => handleInputChange(item.id, 'ip', value)}
                    >
                      <TableInput defaultValue={item.ip} />
                    </DebouncedWithHOC>
                  </FieldExtra>
                </Col>

                {/* Port (only for destination) */}
                {pathname === '/destination' && (
                  <Col {...itemInputProps}>
                    <FieldExtra
                      label={i18n.t('titles.port')}
                      required
                      duplicated={remoteServer.filter((server) => server.id !== item.id && server.port === item.port).length > 0}
                    >
                      <DebouncedWithHOC
                        delay={500}
                        onDebouncedChange={(value: string) => handleInputChange(item.id, 'port', value)}
                      >
                        <TableInput defaultValue={item.port} />
                      </DebouncedWithHOC>
                    </FieldExtra>
                  </Col>
                )}

                {/* Action */}
                <Col style={{ width: 100 }}>
                  <Button
                    {...deleteModal.triggerProps}
                    onClick={() => deleteModal.trigger(item)}
                  />
                </Col>
              </Row>
            </List.Item>
          )
        }}
        style={{ minWidth: 990 }}
        loading={{
          spinning: tableLoading,
          delay: 500,
          indicator: <LoadingIcon2 className="spin-animation" />,
        }}
      />
      <Modal
        {...deleteModal.modalProps}
      />
    </>
  )
}

export default RemoteServerTable
