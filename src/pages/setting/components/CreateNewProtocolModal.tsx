import { useState, useRef, useEffect } from 'react'

import {
  Button, ConfigProvider, Flex, Form, Input, Modal,
} from 'antd'
import { useNavigate } from 'react-router'

import { NewProtocolModaTable } from 'src/components/Table'
import { ModalHooksReturn, useAntModal } from 'src/hooks/useAntModal'
import i18n from 'src/i18n'
import { useCheckProtocolNameMutation } from 'src/services/api'
import { color } from 'src/utils/variables'
import { requiredRules } from 'src/utils/verify'
import 'src/styles/components/modal.css'

type Props = Omit<ModalHooksReturn<unknown>, 'triggerProps'>

function CreateNewProtocolModal({ ...createModalProps }: Props) {
  // ref
  const radioRefs: { current: { [key: string]: HTMLInputElement | null } } = useRef({})

  // router
  const navigate = useNavigate()

  // form
  const [form] = Form.useForm()

  // state
  const [copyProtocolID, setCopyProtocolID] = useState<number | string>('')
  const [protocolName, setProtocolName] = useState<string>()
  const [currentPage, setCurrentPage] = useState<1 | 2>(1)
  const [errorText, setErrorText] = useState<string>('')
  const [isVerify, setIsVerify] = useState({ check: false, text: '' })

  // api
  const [checkProtocolNameMutation] = useCheckProtocolNameMutation()

  // modal
  const leaveModal = useAntModal({
    modalProps: {
      title: i18n.t('modal_titles.leave_page_confirmation'),
      okText: i18n.t('buttons.leave'),
      onOk: async () => {
        createModalProps.dismiss()
        setCurrentPage(1)
        form.resetFields()
      },
      className: 'confirm-modal',
      centered: true,
    },
  })

  const requiredModal = useAntModal({
    modalProps: {
      centered: true,
      className: 'confirm-modal',
      okText: i18n.t('buttons.ok'),
      footer: null,
      styles: {
        body: { padding: '7.5rem 2.5rem' },
      },
    },
  })

  const handleRadioChange = (value: number | string) => {
    setCopyProtocolID(value)
  }

  const handleCancel = () => {
    if (currentPage === 1) {
      createModalProps.dismiss()
      setCurrentPage(1)
    } else {
      leaveModal.trigger()
      setCopyProtocolID('')
    }
    setIsVerify({ check: false, text: '' })
    form.resetFields()
  }

  const handleSecondPage = async () => {
    navigate('create', { state: { protocolName, copyProtocolID } })
    setCurrentPage(1)
  }

  const handleFirstPageError = (e: any) => {
    if (typeof e === 'object' && e !== null) {
      let text: string = i18n.t('form_rules.enter_variable', {
        variable: i18n.t('plain_texts.protocol_name'),
        joinArrays: ' ',
      })
      if ('status' in e) {
        text = e.status === 409
          ? i18n.t('form_rules.variable_unique', {
            variable: i18n.t('titles.protocol_name'),
            joinArrays: ' ',
          })
          : i18n.t('error_contents.server_error')
      }
      setIsVerify({ check: true, text })
    }
  }

  const handleFirstPage = async () => {
    try {
      await form.validateFields()
      const { protocol_name } = form.getFieldsValue()
      setProtocolName(protocol_name)
      await checkProtocolNameMutation({ protocol_name }).unwrap()
      setCurrentPage(2)
      setIsVerify({ check: false, text: '' })
    } catch (e) {
      handleFirstPageError(e)
    }
  }

  const handleSecondPageError = () => {
    setErrorText(i18n.t('form_rules.select_variable', {
      variable: i18n.t('plain_texts.protocol'),
      joinArrays: ' ',
    }))
    requiredModal.trigger()
  }

  const handleSecondPageNext = () => {
    if (copyProtocolID) {
      handleSecondPage()
    } else {
      handleSecondPageError()
    }
  }

  useEffect(() => {
    if (isVerify.check) {
      setErrorText(isVerify.text)
      requiredModal.trigger()
    }
  }, [isVerify])

  return (
    <>
      <ConfigProvider
        theme={{
          token: {
            colorPrimary: color.primary,
          },
        }}
      >
        <Modal
          {...createModalProps.modalProps}
          onCancel={handleCancel}
          width={currentPage === 1 ? 400 : 800}
          className="create-new-protocol-modal"
          footer={null}
          destroyOnClose
        >
          <Form form={form} layout="vertical">
            {
              currentPage === 1 ? (
                <main style={{ padding: '2rem 0' }}>
                  <Form.Item
                    label={i18n.t('titles.protocol_name')}
                    name="protocol_name"
                    rules={requiredRules(i18n.t('titles.protocol_name'))}
                  >
                    <Input placeholder={i18n.t('form_placeholders.enter_variable', {
                      variable: i18n.t('titles.protocol_name'),
                      joinArrays: ' ',
                    })}
                    />
                  </Form.Item>
                  <Flex justify="end" gap={16}>
                    <Button onClick={handleCancel}>
                      {i18n.t('buttons.cancel')}
                    </Button>
                    <Button type="primary" onClick={handleFirstPage}>
                      {i18n.t('buttons.next')}
                    </Button>
                  </Flex>
                </main>
              ) : (
                <Form.Item>
                  <main style={{ padding: '1rem 0' }}>
                    <Flex justify="space-between" align="center" style={{ marginBottom: '1rem' }}>
                      <h3 style={{ margin: 0 }}>
                        {i18n.t('modal_titles.select_protocol_to_copy')}
                      </h3>

                      <Flex gap={16}>
                        <Button onClick={handleCancel}>
                          {i18n.t('buttons.cancel')}
                        </Button>
                        <Button type="primary" onClick={handleSecondPageNext}>
                          {i18n.t('buttons.create')}
                        </Button>
                      </Flex>

                    </Flex>
                    <NewProtocolModaTable
                      copyProtocolID={copyProtocolID}
                      setCopyProtocolID={setCopyProtocolID}
                      radioRefs={radioRefs}
                    />
                  </main>
                </Form.Item>
              )
            }
          </Form>
        </Modal>
      </ConfigProvider>

      {/* leave modal */}
      <Modal {...leaveModal.modalProps}>
        {i18n.t('modal_contents.leave_page_confirmation')}
      </Modal>

      {/* required modal */}
      <Modal {...requiredModal.modalProps}>
        {errorText}
      </Modal>
    </>
  )
}

export default CreateNewProtocolModal
