import { useEffect, useState } from 'react'

import {
  Button, Form, Input, List, Modal, Row, Col,
} from 'antd'

import { LoadingIcon2, XCircleIcon } from 'src/assets/icons'
import { TableInput } from 'src/components/Form'
import { AddLinkModal } from 'src/components/Modal'
import { useAntModal } from 'src/hooks/useAntModal'
import i18n from 'src/i18n'
import { useAppSelector } from 'src/store/hook'
import { requiredRules, duplicatedRules } from 'src/utils/verify'

import 'src/styles/components/ListLayout.css'
import DebouncedWithHOC from '../../../components/DebouncedWithHOC'
import FieldExtra from '../../../components/Form/FieldExtra'

interface Props {
  pathname: string
  tableLoading: boolean
  handleAdd: (data: any) => void
  handleDelete: (data: FolderType) => void
  handleChange: (id: number, fieldName: string, data: FolderType[keyof FolderType]) => void
}

type FiledName = keyof Omit<FolderType, 'id'>

const itemInputProps = {
  style: { flex: 3, minWidth: 200 },
}

function FolderTable({
  pathname,
  tableLoading,
  handleAdd,
  handleChange,
  handleDelete,
}: Props) {
  const [form] = Form.useForm()
  const [openModal, setOpenModal] = useState<boolean>(false)
  const { folder } = useAppSelector((state) => state.remoteReducer)

  const showModal = async () => {
    try {
      await form.validateFields()
      setOpenModal(true)
    } catch (e) {
      console.error(e)
    }
  }

  const onClick = (account?: string, password?: string) => {
    const { name, description, path } = form.getFieldsValue()
    let data = { name, description, path }
    if (account && password) {
      data = { ...data, ...{ account, password } }
    }
    handleAdd(data)
    form.resetFields()
  }

  const deleteModal = useAntModal({
    triggerProps: {
      icon: <XCircleIcon />,
      htmlType: 'button',
      style: {
        backgroundColor: 'transparent',
        cursor: 'pointer',
        lineHeight: '32px',
      },
    },
    modalProps: {
      render(record) {
        return i18n.t('modal_contents.delete_confirmation', { item: `"${record?.name}"`, joinArrays: ' ' })
      },
      title: i18n.t('modal_titles.delete_confirmation'),
      className: 'confirm-modal',
      okText: i18n.t('buttons.delete'),
      onOk: handleDelete,
    },
  })

  useEffect(() => {
    form.resetFields()
  }, [pathname])

  return (
    <>
      <List<FolderType>
        dataSource={folder}
        header={(
          <Form form={form} name="Folder">
            <Row gutter={[32, 0]} style={{ width: '100%', flexWrap: 'nowrap' }}>
              {/* Name Input */}
              <Col {...itemInputProps}>
                <Form.Item
                  label={i18n.t('titles.name')}
                  name="name"
                  labelCol={{ span: 24 }}
                  rules={[
                    requiredRules(i18n.t('titles.name')),
                    duplicatedRules('name', folder),
                  ]}
                >
                  <Input placeholder={i18n.t('form_placeholders.enter_variable', {
                    variable: i18n.t('titles.name'),
                    joinArrays: ' ',
                  })}
                  />
                </Form.Item>
              </Col>

              {/* Description Input */}
              <Col {...itemInputProps}>
                <Form.Item
                  label={i18n.t('titles.description')}
                  name="description"
                  labelCol={{ span: 24 }}
                >
                  <Input placeholder={i18n.t('form_placeholders.enter_variable', {
                    variable: i18n.t('titles.description'),
                    joinArrays: ' ',
                  })}
                  />
                </Form.Item>
              </Col>

              {/* Path Input */}
              <Col {...itemInputProps}>
                <Form.Item
                  label={i18n.t('titles.path')}
                  name="path"
                  labelCol={{ span: 24 }}
                  rules={[
                    requiredRules(i18n.t('titles.path')),
                    duplicatedRules('path', folder),
                  ]}
                >
                  <Input placeholder={i18n.t('form_placeholders.enter_variable', {
                    variable: i18n.t('titles.path'),
                    joinArrays: ' ',
                  })}
                  />
                </Form.Item>
              </Col>

              {/* Add Button */}
              <Col style={{ width: 100 }}>
                <Button
                  onClick={showModal}
                  variant="outlined"
                  color="primary"
                  style={{ marginTop: 30, height: '31px', lineHeight: 0 }}
                  htmlType="submit"
                >
                  {i18n.t('buttons.add')}
                </Button>
              </Col>
            </Row>
          </Form>
        )}
        renderItem={(item) => (
          <List.Item>
            <Row gutter={[32, 0]} style={{ width: '100%', alignItems: 'center' }}>
              {/* Name */}
              <Col {...itemInputProps}>
                <FieldExtra
                  label={i18n.t('titles.name')}
                  required
                  duplicated={folder.filter((f) => f.id !== item.id && f.name === item.name).length > 0}
                >
                  <DebouncedWithHOC
                    delay={500}
                    onDebouncedChange={(value: string) => handleChange(item.id, 'name', value)}
                  >
                    <TableInput defaultValue={item.name} />
                  </DebouncedWithHOC>
                </FieldExtra>
              </Col>

              {/* Description */}
              <Col {...itemInputProps}>
                <FieldExtra label={i18n.t('titles.description')}>
                  <DebouncedWithHOC
                    delay={500}
                    onDebouncedChange={(value: string) => handleChange(item.id, 'description', value)}
                  >
                    <TableInput defaultValue={item.description} />
                  </DebouncedWithHOC>
                </FieldExtra>
              </Col>

              {/* Path */}
              <Col {...itemInputProps}>
                <FieldExtra
                  label={i18n.t('titles.path')}
                  required
                  duplicated={folder.filter((f) => f.id !== item.id && f.path === item.path).length > 0}
                >
                  <DebouncedWithHOC
                    delay={500}
                    onDebouncedChange={(value: string) => handleChange(item.id, 'path', value)}
                  >
                    <TableInput defaultValue={item.path} />
                  </DebouncedWithHOC>
                </FieldExtra>
              </Col>

              {/* Action */}
              <Col style={{ width: 100 }}>
                <Button
                  {...deleteModal.triggerProps}
                  onClick={() => deleteModal.trigger(item)}
                />
              </Col>
            </Row>
          </List.Item>
        )}
        style={{ minWidth: 990 }}
        loading={{
          spinning: tableLoading,
          delay: 500,
          indicator: <LoadingIcon2 className="spin-animation" />,
        }}
      />
      <AddLinkModal
        modalLinkBtn={onClick}
        openModal={openModal}
        setOpenModal={setOpenModal}
      />
      <Modal {...deleteModal.modalProps} />
    </>
  )
}

export default FolderTable
