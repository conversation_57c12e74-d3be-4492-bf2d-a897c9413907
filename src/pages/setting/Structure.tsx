import { useEffect, useState } from 'react'

import {
  Button, Flex, Form, Modal,
} from 'antd'
import { Content, Footer } from 'antd/es/layout/layout'
import trim from 'lodash-es/trim'

import { Search } from 'src/components/Form'
import { Head } from 'src/components/Head'
import LeaveConfirmModal from 'src/components/Modal/LeaveConfirmModal'
import { StructureTable } from 'src/components/Table'
import useAlert from 'src/hooks/useAlert'
import { useAntModal } from 'src/hooks/useAntModal'
import i18n from 'src/i18n'
import { useGetStructureMutation, usePutStructureMutation } from 'src/services/api'
import { useAppSelector, useAppDispatch } from 'src/store/hook'
import { resetStructureState } from 'src/store/reducers/structureSlice'
import {
  resetAntModalProps, saveAntModalProps,
} from 'src/utils/saveButton'

import 'src/styles/components/tableLayout.css'

function Structure() {
  const [form] = Form.useForm()
  const [tableLoading, setTableLoading] = useState<boolean>(false)
  const { structures, updateStructureIds } = useAppSelector((state) => state.structureReducer)
  const [getStructureMutation] = useGetStructureMutation()
  const [putStructureMutation] = usePutStructureMutation()
  const dispatch = useAppDispatch()

  // hook
  const handleAlert = useAlert()

  // api
  const getStructure = async () => {
    setTableLoading(true)
    try {
      await getStructureMutation({ name: form.getFieldsValue().searchName }).unwrap()
    } catch (e) {
      handleAlert({ content: (e as Err).data?.detail }, 'Msg', 'error')
    } finally {
      setTableLoading(false)
    }
  }

  const resetModal = useAntModal({
    ...resetAntModalProps,
    modalProps: {
      ...resetAntModalProps?.modalProps,
      onOk: getStructure,
    },
  })
  const saveModal = useAntModal(saveAntModalProps)

  const handleSave = async () => {
    const updateStructureData = structures.filter((item) => updateStructureIds.includes(item.id))
    try {
      await putStructureMutation({ structures: updateStructureData }).unwrap()
      await getStructure()
      saveModal.dismiss()
    } catch (e) {
      handleAlert({ title: i18n.t('error_titles.save'), content: (e as Err).data?.detail }, 'Msg', 'error')
    }
  }

  const nameList = structures
    .map(({ efai_structure_name, customized_name }) => ((customized_name)
      ? ([efai_structure_name, customized_name])
      : ([efai_structure_name])))
    .flat()

  const isHaveRepeatNames = (): boolean => {
    const set = new Set()
    let repeat: boolean = false
    nameList.forEach((item) => {
      const trimValue = trim(item)
      if (set.has(trimValue)) { repeat = true }
      set.add(trimValue)
    })
    return repeat
  }

  const checkingRepeatNames = () => {
    const isRepeat = isHaveRepeatNames()
    if (isRepeat) {
      saveModal.trigger(undefined, {
        title: '',
        children: i18n.t('form_rules.customized_name_duplicate'),
        footer: null,
        styles: {
          body: {
            padding: '7.75rem 1.25rem 8.25rem',
          },
        },
      })
    } else {
      saveModal.trigger()
    }
  }

  useEffect(() => {
    getStructure()
    return () => {
      dispatch(resetStructureState())
    }
  }, [])

  return (
    <>
      <Head>{i18n.t('titles.structures')}</Head>
      <Content style={{ flex: 1, gap: '1.5rem' }}>
        <Search form={form} handleSearch={getStructure} />
        <StructureTable tableLoading={tableLoading} />
      </Content>
      {!!updateStructureIds.length && (
        <Footer>
          <Flex justify="end" gap={16}>
            <Button {...resetModal.triggerProps} />
            <Button {...saveModal.triggerProps} onClick={checkingRepeatNames} />
          </Flex>
        </Footer>
      )}
      <Modal {...resetModal.modalProps} />
      <Modal {...saveModal.modalProps} onOk={handleSave} />
      <LeaveConfirmModal condition={!!updateStructureIds.length} />
    </>
  )
}

export default Structure
