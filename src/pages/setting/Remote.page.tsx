import { useEffect, useState } from 'react'

import {
  <PERSON>ton, Modal, Tabs, TabsProps,
} from 'antd'
import { Content, Footer } from 'antd/es/layout/layout'
import { useLocation } from 'react-router'

import { Head } from 'src/components/Head'
import LeaveConfirmModal from 'src/components/Modal/LeaveConfirmModal'
import useAlert from 'src/hooks/useAlert'
import { useAntModal } from 'src/hooks/useAntModal'
import i18n from 'src/i18n'
import {
  useGetRemoteMutation,
  useGetRemoteStatusMutation,
  usePutRemoteMutation,
  usePostRemoteServerMutation,
  useDeleteRemoteServerMutation,
  usePostFolderMutation,
  useDeleteFolderMutation,
} from 'src/services/api'
import { useAppSelector, useAppDispatch } from 'src/store/hook'
import { resetRemoteState, updateRemote } from 'src/store/reducers/remoteSlice'
import { saveAntModalProps } from 'src/utils/saveButton'

type RequiredErrorsType = {
  remoteServer: boolean,
  folder: boolean,
}

function RemotePage() {
  // router
  const { pathname } = useLocation()
  const type = pathname.replace('/', '').toUpperCase()
  // state
  const [tableLoading, setTableLoading] = useState<boolean>(false)

  // store
  const dispatch = useAppDispatch()
  const {
    remoteServer, folder, updateRemoteServerIds, updateFolderIds,
  } = useAppSelector((state) => state.remoteReducer)
  const { message } = useAppSelector((state) => state.websocketReducer)
  // api
  const [getRemoteMutation] = useGetRemoteMutation()
  const [getRemoteStatusMutation] = useGetRemoteStatusMutation()
  const [putRemoteMutation] = usePutRemoteMutation()
  const [postRemoteServerMutation] = usePostRemoteServerMutation()
  const [deleteRemoteServerMutation] = useDeleteRemoteServerMutation()
  const [postFolderMutation] = usePostFolderMutation()
  const [deleteFolderMutation] = useDeleteFolderMutation()
  // hook
  const handleAlert = useAlert()

  const saveModal = useAntModal({ ...saveAntModalProps })

  const getRemote = async () => {
    setTableLoading(true)
    try {
      await getRemoteMutation({ type }).unwrap()
    } catch (e) {
      handleAlert({ content: (e as Err).data?.detail }, 'Msg', 'error')
    }
    setTableLoading(false)
  }

  const getRemoteStatus = async () => {
    try {
      await getRemoteStatusMutation({ type }).unwrap()
    } catch (e) {
      // Don't use useAlert
      console.error(e)
    }
  }

  const handleAddRemoteServer = async (data: AddRemoteServerType) => {
    try {
      await postRemoteServerMutation({
        type,
        name: data.name,
        description: data.description || '',
        ae_title: data.ae_title,
        ip: data.ip,
        port: data.port,
      }).unwrap()
      await getRemoteMutation({ type })
    } catch (e) {
      handleAlert({ title: i18n.t('error_titles.link'), content: i18n.t('error_contents.link') }, 'Msg', 'error')
    }
  }

  const handleDeleteRemoteServer = async (data: RemoteServerType) => {
    try {
      await deleteRemoteServerMutation({
        type,
        ae_title: data.ae_title,
        ip: data.ip,
        port: data.port,
      }).unwrap()
      await getRemoteMutation({ type })
    } catch (e) {
      handleAlert({ content: (e as Err).data?.detail }, 'Msg', 'error')
    }
  }
  const handleChangeRemoteServer = (id: number, fieldName: string, data: RemoteServerType[keyof RemoteServerType]) => {
    dispatch(updateRemote({ remote: 'remote server', data: { id, [fieldName]: data } }))
  }
  const handleAddFolder = async (data: AddFolderType) => {
    try {
      await postFolderMutation({
        type,
        folder_name: data.name,
        folder_description: data.description || '',
        folder_path: data.path,
        folder_account: data.account,
        folder_password: data.password,
      }).unwrap()
      await getRemoteMutation({ type })
    } catch (e) {
      handleAlert({ title: i18n.t('error_titles.link'), content: i18n.t('error_contents.link') }, 'Msg', 'error')
    }
  }
  const handleDeleteFolder = async (data: FolderType) => {
    try {
      await deleteFolderMutation({ type, folder_name: data.name, folder_path: data.path }).unwrap()
      await getRemoteMutation({ type })
    } catch (e) {
      handleAlert({ content: (e as Err).data?.detail }, 'Msg', 'error')
    }
  }
  const handleChangeFolder = (id: number, fieldName: string, data: FolderType[keyof FolderType]) => {
    dispatch(updateRemote({ remote: 'folder', data: { id, [fieldName]: data } }))
  }

  const handleSave = async () => {
    if (updateRemoteServerIds.length || updateFolderIds.length) {
      const updateRemoteServer = remoteServer.filter((item) => updateRemoteServerIds.includes(item.id))
      const updateFolder = folder.filter((item) => updateFolderIds.includes(item.id))
      try {
        await putRemoteMutation({ type, remote_server_list: updateRemoteServer, folder_list: updateFolder }).unwrap()
        getRemoteMutation({ type })
        saveModal.dismiss()
      } catch (e) {
        handleAlert({ title: i18n.t('error_titles.save'), content: (e as Err).data?.detail }, 'Msg', 'error')
      }
    }
  }

  const triggerSaveModal = async () => {
    let requiredErrors: RequiredErrorsType = {
      remoteServer: false,
      folder: false,
    }
    try {
      const remoteServerRequired = remoteServer
        .some((item) => !item.ip
          || (pathname === '/destination' && !item.port)
          || !item.ae_title)
      const folderRequired = folder.some((item) => !item.path)

      if (remoteServerRequired || folderRequired) {
        requiredErrors = {
          remoteServer: remoteServerRequired,
          folder: folderRequired,
        }
        throw new Error('Have been reqired')
      }

      saveModal.trigger()
    } catch (error) {
      let errMessage = ''
      if (requiredErrors.remoteServer && requiredErrors.folder) {
        errMessage = i18n.t(
          'modal_contents.multiple_items_required',
          { item: '"Remote Server" & "Folder"', joinArrays: ' ' },
        )
      } else if (requiredErrors.remoteServer) {
        errMessage = i18n.t('modal_contents.item_required', { item: 'Remote Server', joinArrays: ' ' })
      } else if (requiredErrors.folder) {
        errMessage = i18n.t('modal_contents.item_required', { item: 'Folder', joinArrays: ' ' })
      }
      saveModal.trigger(undefined, {
        children: errMessage,
        width: 350,
        title: null,
        footer: null,
        centered: true,
        styles: {
          body: {
            padding: '8rem 1.25rem 8.5rem',
            textAlign: 'center',
            lineHeight: 2,
          },
        },
      })
    }
  }

  useEffect(() => {
    getRemote()
    return () => {
      dispatch(resetRemoteState())
    }
  }, [pathname])

  useEffect(() => {
    if (message.content !== 'remote_update') return
    getRemoteStatus()
  }, [message.timestamp])

  const items: TabsProps['items'] = [
    {
      key: 'remote server',
      label: i18n.t('titles.remote_server'),
      children: (
        <RemoteServerTable
          pathname={pathname}
          tableLoading={tableLoading}
          handleAdd={handleAddRemoteServer}
          handleDelete={handleDeleteRemoteServer}
          handleChange={handleChangeRemoteServer}
        />
      ),
    },
    {
      key: 'folder',
      label: i18n.t('titles.folder'),
      children: (
        <FolderTable
          pathname={pathname}
          tableLoading={tableLoading}
          handleAdd={handleAddFolder}
          handleDelete={handleDeleteFolder}
          handleChange={handleChangeFolder}
        />
      ),
    },
  ]

  return (
    <>
      <Head>{i18n.t(`titles.${pathname.split('/')[1]}`)}</Head>
      <Content style={{ padding: ' 2rem 2rem 1rem' }}>
        <Tabs
          defaultActiveKey="1"
          items={items}
          rootClassName="scroll-area-tabs"
        />
      </Content>
      {
        updateRemoteServerIds.length || updateFolderIds.length ? (
          <Footer style={{ display: 'flex', justifyContent: 'flex-end', paddingTop: 0 }}>
            <Button {...saveModal.triggerProps} onClick={triggerSaveModal} />
          </Footer>
        ) : null
      }
      <Modal {...saveModal.modalProps} onOk={handleSave} />
      <LeaveConfirmModal condition={!!updateRemoteServerIds.length || !!updateFolderIds.length} />
    </>
  )
}

export default RemotePage
