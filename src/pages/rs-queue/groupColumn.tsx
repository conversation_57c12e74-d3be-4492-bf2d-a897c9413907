import type { ColumnsType } from 'antd/es/table/interface'

import ProgressBar from 'src/components/ProgressBar'
import i18n from 'src/i18n'
import { WorkStatusEnum } from 'src/utils/enum'
import { formattedStatus, withoutTime } from 'src/utils/helper'
import { color } from 'src/utils/variables'

export const groupColumn: ColumnsType<WorklistGroupType> = [
  {
    title: i18n.t('titles.patient_id'),
    dataIndex: 'patient_id',
    render: (id) => <div style={{ paddingLeft: '16px' }}>{id}</div>,
    key: 'patient_id',
    width: '100px',
    sorter: false,
    showSorterTooltip: false,
    ellipsis: true,
  },
  {
    title: i18n.t('titles.patient_name'),
    dataIndex: 'patient_name',
    key: 'patient_name',
    width: '100px',
    sorter: false,
    showSorterTooltip: false,
    render: (value, { progress }) => {
      const studyStatus: string[] = [WorkStatusEnum.PROCESSING, WorkStatusEnum.PENDING, WorkStatusEnum.SUSPENDED]
      if (studyStatus.includes(value)) {
        return (
          <ProgressBar
            status={value}
            progress={progress * 100}
          />
        )
      }
      return (
        <div className="progress-title">
          {formattedStatus(value)}
        </div>
      )
    },
  },
  {
    title: i18n.t('titles.study_date'),
    dataIndex: 'study_date',
    key: 'study_date',
    width: '100px',
    sorter: false,
    showSorterTooltip: false,
    render: (_, { study_date }) => (
      <p style={{
        minWidth: 90,
        color: color.gray[1].default,
      }}
      >
        {withoutTime(study_date)}
      </p>
    ),
  },
]
