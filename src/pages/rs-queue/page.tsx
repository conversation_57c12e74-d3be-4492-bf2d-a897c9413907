// React import removed as not needed

import { Form } from 'antd'

import WorklistLayout from '@/layouts/WorklistLayout'

import SubTable from './SubTable'
import { useListManager } from './useListManager'

const STATUS_OPTIONS = [
  { label: 'All', value: '' },
  { label: 'Pending', value: 'PENDING' },
  { label: 'Suspended', value: 'SUSPENDED' },
  { label: 'Cancelled', value: 'CANCELLED' },
]

function RsQueuePage() {
  const [form] = Form.useForm()
  const rsQueueManager = useListManager()

  const handleSearch = () => {
    const formValues = form.getFieldsValue()
    rsQueueManager.handleSearch({
      patientId: formValues.searchName,
    })
  }

  return (
    <WorklistLayout
      form={form}
      pageTitle="titles.rs_queue"
      searchOptions={STATUS_OPTIONS}
      onSearch={handleSearch}
      tableController={rsQueueManager}
    >
      <SubTable />
    </WorklistLayout>
  )
}

export default RsQueuePage
