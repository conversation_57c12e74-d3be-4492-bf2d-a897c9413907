// Type validation test for RsQueueGroupType
// This file helps ensure our type definitions match the expected API structure

import { mockRsQueueGroups } from 'src/mocks/data/rs-queue.mock'

// Test function to validate the type structure
function validateRsQueueGroupType() {
  const sampleGroup = mockRsQueueGroups[0]
  
  // These should all be accessible without TypeScript errors
  const requiredFields = {
    id: sampleGroup.id,
    patient_id: sampleGroup.patient_id,
    patient_name: sampleGroup.patient_name,
    study_date: sampleGroup.study_date,
    study_uid: sampleGroup.study_uid,
  }
  
  const optionalFields = {
    status: sampleGroup.status,
    progress: sampleGroup.progress,
    total_rs_count: sampleGroup.total_rs_count,
    completed_rs_count: sampleGroup.completed_rs_count,
    last_modified: sampleGroup.last_modified,
    study_description: sampleGroup.study_description,
  }
  
  // Validate the structure matches the expected API format
  const apiFormat = {
    patient_id: "123456789",
    patient_name: "Ming LI", 
    study_date: "2025-05-26",
    study_uid: "*******"
  }
  
  // Type assertion to ensure compatibility
  const isCompatible: boolean = 
    typeof requiredFields.patient_id === typeof apiFormat.patient_id &&
    typeof requiredFields.patient_name === typeof apiFormat.patient_name &&
    typeof requiredFields.study_date === typeof apiFormat.study_date &&
    typeof requiredFields.study_uid === typeof apiFormat.study_uid
  
  return isCompatible
}

// Export for potential use in actual tests
export { validateRsQueueGroupType }
