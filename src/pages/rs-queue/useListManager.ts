import { useEffect } from 'react'

import { useGetRsQueueStudiesQuery } from 'src/services/api'
import { useAppSelector, useAppDispatch } from 'src/store/hook'
import {
  setRsQueueGroupFocus,
  setCurrentPage,
  setSorter,
  setSearchParameter
} from 'src/store/reducers/rsQueueSlice'

import { groupColumn } from './groupColumn'

export function useListManager() {
  const dispatch = useAppDispatch()
  const {
    rsQueueGroupFocus,
    searchParameter,
    sorter,
    pagination,
  } = useAppSelector((state) => state.rsQueueReducer)
  const { message } = useAppSelector((state) => state.websocketReducer)

  const { data, isLoading, refetch } = useGetRsQueueStudiesQuery({
    patient_id: searchParameter.patientId,
    order_key: sorter.orderKey,
    ascend: sorter.ascend,
    page: pagination.current,
  })

  const handleSetRowFocus = (study_uid: string) => {
    dispatch(setRsQueueGroupFocus(study_uid))
  }

  const handleSortChange = (orderKey: string, ascend: boolean) => {
    dispatch(setSorter({ orderKey, ascend }))
  }

  const handlePageChange = (currentPage: number) => {
    dispatch(setCurrentPage(currentPage))
  }

  const handleSearch = (searchParams: {
    patientId?: string
  }) => {
    dispatch(setSearchParameter(searchParams))
  }

  useEffect(() => {
    if (message.content === 'rs_queue_update') {
      refetch()
    }
  }, [message.timestamp])

  return {
    columns: groupColumn,
    data: data?.studies || [],
    // pagination
    pagination: {
      current: pagination.current,
      total: pagination.total,
      pageSize: pagination.pageSize,
    },
    // api manager
    isLoading,
    apiRefetch: refetch,
    // focus
    rowFocus: rsQueueGroupFocus,
    rowFocusKey: 'study_uid',
    // function
    handleSetRowFocus,
    handleSortChange,
    handlePageChange,
    handleSearch,
  }
}
