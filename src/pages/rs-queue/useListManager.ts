import { useEffect } from 'react'

import { useGetWorklistGroupQuery } from 'src/services/api'
import { useAppSelector, useAppDispatch } from 'src/store/hook'
import { setTaskListFocus, setCurrentPage, setSorter } from 'src/store/reducers/taskListSlice'

import { groupColumn } from './groupColumn'

export function useListManager() {
  const dispatch = useAppDispatch()
  const {
    taskListFocus,
    searchParameter,
    sorter,
    pagination,
  } = useAppSelector((state) => state.taskListSlice)
  const { message } = useAppSelector((state) => state.websocketReducer)

  const { data, isLoading, refetch } = useGetWorklistGroupQuery({
    patient_id: searchParameter.patientId,
    study_status: searchParameter.studyStatus,
    order_key: sorter.orderKey,
    ascend: sorter.ascend,
    page: pagination.current,
  })

  const handleSetRowFocus = (id: number) => {
    dispatch(setTaskListFocus(id))
  }

  const handleSortChange = (orderKey: string, ascend: boolean) => {
    dispatch(setSorter({ orderKey, ascend }))
  }

  const handlePageChange = (currentPage: number) => {
    dispatch(setCurrentPage(currentPage))
  }

  useEffect(() => {
    if (message.content === 'worklist_update') {
      refetch()
    }
  }, [message.timestamp])

  return {
    columns: groupColumn,
    data: data?.worklist_group || [],
    // pagination
    pagination: {
      current: pagination.current,
      total: pagination.total,
      pageSize: pagination.pageSize,
    },
    // api manager
    isLoading,
    apiRefetch: refetch,
    // focus
    rowFocus: taskListFocus,
    rowFocusKey: 'id',
    // function
    handleSetRowFocus,
    handleSortChange,
    handlePageChange,
  }
}
