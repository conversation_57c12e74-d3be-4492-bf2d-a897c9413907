import {
  useGetRsQueueItemsQuery,
  useCheckRsQueueItemStatusMutation,
  useDeleteRsQueueItemMutation,
} from 'src/services/api'
import { useAppSelector } from 'src/store/hook'

export function useRsQueueItems() {
  const { rsQueueGroupFocus, rsQueueGroups } = useAppSelector((state) => state.rsQueueReducer)

  // Find the selected group by study_uid
  const selectedGroup = rsQueueGroups.find((group) => group.study_uid === rsQueueGroupFocus)

  // Get RS queue items for the selected group
  const {
    data: rsQueueItemsData,
    isLoading: isLoadingItems,
    refetch: refetchItems,
  } = useGetRsQueueItemsQuery(
    { rs_queue_group_id: selectedGroup?.study_uid || '' },
    { skip: !selectedGroup },
  )

  // Mutations
  const [checkItemStatus] = useCheckRsQueueItemStatusMutation()
  const [deleteItem] = useDeleteRsQueueItemMutation()

  const rsQueueItems = rsQueueItemsData?.rs_queue_items || []

  const handleCheckStatus = async (itemId: number) => {
    try {
      const result = await checkItemStatus({ id: itemId }).unwrap()
      return result.status
    } catch (error: unknown) {
      // Handle different error scenarios
      const err = error as { status?: number }
      if (err.status === 400) {
        throw new Error('NO_MATCH')
      }
      if (err.status === 500) {
        throw new Error('FAILED')
      }
      throw error
    }
  }

  const handleDeleteItem = async (itemId: number) => {
    await deleteItem({ id: itemId }).unwrap()
    // Refetch items after deletion
    refetchItems()
  }

  return {
    rsQueueItems,
    isLoadingItems,
    refetchItems,
    handleCheckStatus,
    handleDeleteItem,
    selectedGroup,
  }
}
