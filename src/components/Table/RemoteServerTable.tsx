import React, { useEffect } from 'react'

import {
  Button, Col, Form, Input, List, Modal, Row,
} from 'antd'

import { LoadingIcon2, XCircleIcon } from 'src/assets/icons'
import { TableInput } from 'src/components/Form'
import { useAntModal } from 'src/hooks/useAntModal'
import i18n from 'src/i18n'
import { useAppSelector } from 'src/store/hook'
import { requiredRules, duplicatedRules } from 'src/utils/verify'

import DebouncedWithHOC from '../DebouncedWithHOC'
import FieldExtra from '../Form/FieldExtra'

import 'src/styles/components/ListLayout.css'

interface Props {
  pathname: string
  tableLoading: boolean
  handleAdd: (data: any) => void
  handleDelete: (data: RemoteServerType) => void
  handleChange: (id: number, fieldName: string, data: RemoteServerType[keyof RemoteServerType]) => void
}

type FiledName = keyof Omit<RemoteServerType, 'id'>

const getPlaceholder = (titleKey: string): string => i18n.t(
  'form_placeholders.enter_variable',
  { variable: i18n.t(`titles.${titleKey}`), joinArrays: ' ' },
)

const itemInputProps = {
  style: { flex: 3, minWidth: 200 },
}

function RemoteServerTable({
  pathname,
  tableLoading,
  handleAdd,
  handleChange,
  handleDelete,
}: Props) {
  const { remoteServer } = useAppSelector((state) => state.remoteReducer)
  const [form] = Form.useForm()

  const showModal = async () => {
    try {
      await form.validateFields()
    } catch (e) {
      throw new Error('form valideted error')
    }
  }

  // modal
  const addModal = useAntModal({
    triggerProps: {
      onClick: showModal,
      variant: 'outlined',
      color: 'primary',
      style: { marginTop: 40, height: '31px', lineHeight: 0 },
      children: i18n.t('buttons.add'),
      htmlType: 'submit',
    },
    modalProps: {
      styles: {
        body: { padding: '4.5rem 0' },
      },
      title: i18n.t('modal_titles.link_to', { value: i18n.t('titles.remote_server'), joinArrays: ' ' }),
      okText: i18n.t('buttons.link'),
      onOk: () => {
        const {
          // eslint-disable-next-line @typescript-eslint/naming-convention
          name, description, ae_title, ip, port,
        } = form.getFieldsValue()
        handleAdd({
          name, description, ae_title, ip, port,
        })
        form.resetFields()
      },
      width: 350,
      centered: true,
    },
  })

  const deleteModal = useAntModal<RemoteServerType>({
    triggerProps: {
      children: <XCircleIcon />,
      htmlType: 'button',
      className: 'ant-list-delete',
      style: {
        backgroundColor: 'transparent',
        cursor: 'pointer',
        lineHeight: '32px',
        boxShadow: 'none',
      },
    },
    modalProps: {
      render(record) {
        return i18n.t('modal_contents.delete_confirmation', { item: `"${record?.name}"`, joinArrays: ' ' })
      },
      title: i18n.t('modal_titles.delete_confirmation'),
      className: 'confirm-modal',
      okText: i18n.t('buttons.delete'),
      onOk: handleDelete,
    },
  })

  useEffect(() => {
    form.resetFields()
  }, [pathname])

  return (
    <>
      <List<RemoteServerType>
        dataSource={remoteServer}
        header={(
          <Form form={form} name="RemoteServer">
            <Row
              gutter={[32, 0]}
              style={{ width: '100%', flexWrap: 'nowrap' }}
            >
              {/* Name */}
              <Col {...itemInputProps}>
                <Form.Item
                  label={i18n.t('titles.name')}
                  name="name"
                  labelCol={{ span: 24 }}
                  required
                  rules={[requiredRules('name'), duplicatedRules('name', remoteServer)]}
                >
                  <Input placeholder={getPlaceholder('name')} />
                </Form.Item>
              </Col>
              {/* Description */}
              <Col {...itemInputProps}>
                <Form.Item
                  label={i18n.t('titles.description')}
                  name="description"
                  labelCol={{ span: 24 }}
                >
                  <Input placeholder={getPlaceholder('description')} />
                </Form.Item>
              </Col>
              {/* AE Title */}
              <Col {...itemInputProps}>
                <Form.Item
                  label={i18n.t('titles.ae_title')}
                  name="ae_title"
                  labelCol={{ span: 24 }}
                  required
                  rules={[requiredRules('ae_title')]}
                >
                  <Input placeholder={getPlaceholder('ae_title')} />
                </Form.Item>
              </Col>
              {/* IP */}
              <Col {...itemInputProps}>
                <Form.Item
                  label={i18n.t('titles.ip')}
                  name="ip"
                  labelCol={{ span: 24 }}
                  required
                  rules={[requiredRules('ip')]}
                >
                  <Input placeholder={getPlaceholder('ip')} />
                </Form.Item>
              </Col>
              {/* Port (only for '/destination') */}
              {pathname === '/destination' && (
                <Col {...itemInputProps}>
                  <Form.Item
                    label={i18n.t('titles.port')}
                    name="port"
                    labelCol={{ span: 24 }}
                    rules={[requiredRules('port')]}
                  >
                    <Input placeholder={getPlaceholder('port')} />
                  </Form.Item>
                </Col>
              )}
              {/* Action (button column, no minWidth) */}
              <Col style={{ width: 100 }}>
                <Button {...addModal.triggerProps} />
                <Modal {...addModal.modalProps}>
                  {i18n.t('modal_contents.add_link_confirmation')}
                </Modal>
              </Col>
            </Row>
          </Form>
        )}
        renderItem={(item) => {
          const getFieldValue = (fieldName: FiledName) => !item[fieldName]

          const getErrorMessage = (fieldName: FiledName) => (
            getFieldValue(fieldName)
              ? i18n.t('form_rules.enter_variable', {
                variable: i18n.t(`plain_texts.${fieldName}`),
                joinArrays: ' ',
              })
              : ''
          )
          const inputStatus = (fieldName: FiledName) => (getFieldValue(fieldName) ? 'error' : undefined)

          return (
            <List.Item>
              <Row
                gutter={[32, 0]}
                style={{ width: '100%', flexWrap: 'nowrap' }}
              >
                {/* Name */}
                <Col {...itemInputProps}>
                  <div
                    style={{
                      paddingLeft: '8px',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '.75rem',
                    }}
                  >
                    {pathname === '/destination' && (
                      <span
                        className={`traffic-light ${item.connect_status === 'CONNECT' ? 'green' : 'red'}`}
                      />
                    )}
                    {item.name}
                  </div>
                </Col>
                {/* Description */}
                <Col {...itemInputProps}>
                  <TableInput
                    id={item.id}
                    name="description"
                    value={item.description}
                    handleChange={handleChange}
                  />
                </Col>
                {/* AE Title */}
                <Col {...itemInputProps}>
                  <FieldExtra
                    in={getFieldValue('ae_title')}
                    message={getErrorMessage('ae_title')}
                    status="error"
                  >
                    <DebouncedWithHOC
                      value={item.ae_title}
                      onChange={(value) => handleChange(item.id, 'ae_title', value)}
                    >
                      <Input required placeholder={getPlaceholder('ae_title')} status={inputStatus('ae_title')} />
                    </DebouncedWithHOC>

                  </FieldExtra>
                </Col>
                {/* IP */}
                <Col {...itemInputProps}>
                  <FieldExtra
                    in={getFieldValue('ip')}
                    message={getErrorMessage('ip')}
                    status="error"
                  >
                    <DebouncedWithHOC
                      value={item.ip}
                      onChange={(value) => handleChange(item.id, 'ip', value)}
                    >
                      <Input required placeholder={getPlaceholder('ip')} status={inputStatus('ip')} />
                    </DebouncedWithHOC>

                  </FieldExtra>
                </Col>
                {/* Port (only for '/destination') */}
                {pathname === '/destination' && (
                  <Col {...itemInputProps}>
                    <FieldExtra
                      in={getFieldValue('port')}
                      message={getErrorMessage('port')}
                      status="error"
                    >
                      <DebouncedWithHOC
                        value={item.port}
                        onChange={(value) => handleChange(item.id, 'port', value)}
                      >
                        <Input required placeholder={getPlaceholder('port')} />
                      </DebouncedWithHOC>

                    </FieldExtra>
                  </Col>
                )}
                {/* Action */}
                <Col style={{ width: 100 }}>
                  <Button
                    {...deleteModal.triggerProps}
                    onClick={() => deleteModal.trigger(item)}
                  />
                </Col>
              </Row>
            </List.Item>
          )
        }}
        style={{ minWidth: 990 }}
        loading={{
          spinning: tableLoading,
          delay: 500,
          indicator: <LoadingIcon2 className="spin-animation" />,
        }}
      />
      <Modal
        {...deleteModal.modalProps}
      />
    </>
  )
}

export default RemoteServerTable
