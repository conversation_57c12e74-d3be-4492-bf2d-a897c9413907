import {
  But<PERSON>, Modal, Table,
} from 'antd'
import type { ColumnsType } from 'antd/es/table'
import { Link } from 'react-router'

import {
  DeleteIcon, EditIcon, LoadingIcon2,
} from 'src/assets/icons'
import useAlert from 'src/hooks/useAlert'
import { useAntModal } from 'src/hooks/useAntModal'
import i18n from 'src/i18n'
import { useDeleteRsTemplateMutation } from 'src/services/api'
import { useAppDispatch, useAppSelector } from 'src/store/hook'
import { deleteRsTemplate } from 'src/store/reducers/rsTemplatesSlice'
import { withoutTime } from 'src/utils/helper'

import 'src/styles/components/tableLayout.css'

interface Props {
  spinning: boolean
  reload: () => void
}

interface ColumnDataType {
  key: number
  id: number
  template_name: string
  last_modified: string
  description: string
}

function RsTemplatesTable({ spinning, reload }: Props) {
  const { templates } = useAppSelector((state) => state.rsTemplatesReducer)
  const [deleteRsTemplateMutation] = useDeleteRsTemplateMutation()
  const dispatch = useAppDispatch()

  // hook
  const handleAlert = useAlert()
  const deleteModal = useAntModal({
    modalProps: {
      title: i18n.t('modal_titles.delete_confirmation'),
      onOk: async (id: number) => {
        try {
          await deleteRsTemplateMutation({ id }).unwrap()
          dispatch(deleteRsTemplate({ id }))
          reload()
        } catch (e) {
          handleAlert({ content: (e as Err).data?.detail }, 'Msg', 'error')
        }
      },
      width: 350,
      styles: { body: { padding: '4.75rem 0' } },
    },
  })

  const dataSource = templates.map((item, index) => ({
    key: index,
    id: item.id,
    template_name: item.template_name,
    last_modified: withoutTime(item.last_modified),
    description: item.description,
  }))

  const columns: ColumnsType<ColumnDataType> = [
    {
      title: 'Template Name',
      dataIndex: 'template_name',
      key: 'template_name',
      render(_, { template_name }) {
        return (<div style={{ textWrap: 'nowrap' }}> {template_name}</div>)
      },
    },
    {
      title: 'Last Modified',
      dataIndex: 'last_modified',
      key: 'last_modified',
      render(_, { last_modified }) {
        return (<div style={{ textWrap: 'nowrap' }}> {last_modified}</div>)
      },
      width: '200px',
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
    },
    {
      title: 'Operation',
      key: 'operation',
      width: '120px',
      render: (_, { id, template_name }: ColumnDataType) => (
        <div className="icons-btn-group">
          <Button
            className="basic-shadow"
            size="small"
            onClick={() => deleteModal.trigger(id, {
              children: i18n.t(
                'modal_contents.delete_confirmation',
                { item: `"${template_name}"`, joinArrays: ' ' },
              ),
            })}
            icon={<DeleteIcon />}
          />
          <Link className="icon-only" to={`${id}`} style={{ fontSize: 28, borderRadius: 4 }}>
            <EditIcon />
          </Link>
        </div>
      ),
    },
  ]

  return (
    <section style={{ flex: '1 0 0' }}>
      <Table
        scroll={{ x: true, y: 'calc(100% - 45px)' }}
        className="setting-table-container"
        rowKey="id"
        columns={columns}
        dataSource={dataSource}
        pagination={false}
        loading={{
          spinning,
          delay: 500,
          indicator: <LoadingIcon2 className="spin-animation" />,
        }}
      />
      <Modal {...deleteModal.modalProps} />
    </section>
  )
}

export default RsTemplatesTable
