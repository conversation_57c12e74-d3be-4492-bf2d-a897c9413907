import { useEffect } from 'react'

import {
  createBrowserRouter,
  Navigate,
  RouterProvider,
} from 'react-router'

import useParseManual from './hooks/useParseManual'
import NavigationLayout from './layouts/NavigationLayout'
import {
  CreateProtocolPage, ProcessStructuresPage, ProtocolConfigPage, WorklistSetting,
} from './pages/config-settings'
import HistoryPage from './pages/history/page'
import Login from './pages/Login'
import ManualPage from './pages/manual'
import Directions from './pages/manual/Directions'
import FAQ from './pages/manual/FAQ'
import RsQueuePage from './pages/rs-queue/page'
import {
  Structure, Protocol, RemotePage, RsTemplatesPage,
} from './pages/setting'
import TaskListPage from './pages/task-list/page'
import { useAppSelector } from './store/hook/index'

function App() {
  const { isAuth } = useAppSelector((state) => state.authReducer)
  const [jsonData, menu] = useParseManual()

  useEffect(() => {
    const loadingEl = document.getElementById('root-rending')
    if (loadingEl) {
      loadingEl.classList.add('fade-out')

      const removeTimer = setTimeout(() => {
        loadingEl.remove()
      }, 1000) // 與 .fadeOut 2s 相同

      return () => clearTimeout(removeTimer)
    }

    return () => { }
  }, [])

  const router = createBrowserRouter(
    isAuth
      ? [
        {
          path: '/',
          element: <NavigationLayout />,
          children: [
            {
              index: true,
              element: <TaskListPage />,
            },
            {
              path: 'worklist/:id',
              element: <WorklistSetting />,
            },
            {
              path: 'history',
              children: [
                {
                  index: true,
                  element: <HistoryPage />,
                },
                {
                  path: ':id',
                  element: <WorklistSetting isHistory />,
                },
              ],
            },
            {
              path: 'rs',
              children: [
                {
                  path: 'queue',
                  element: <RsQueuePage />,
                },
                {
                  path: 'queue/:id',
                  element: <ProcessStructuresPage />, // Process Structures Page
                },
                {
                  path: 'templates',
                  element: <RsTemplatesPage />,
                },
                {
                  path: 'templates/:id',
                  element: <RsTemplatesPage />,
                },
              ],
            },
            {
              path: 'protocols',
              children: [
                {
                  index: true,
                  element: <Protocol />,
                },
                {
                  path: ':id',
                  element: <ProtocolConfigPage />,
                },
                {
                  path: 'create',
                  element: <CreateProtocolPage />,
                },
              ],
            },
            {
              path: 'structure',
              element: <Structure />,
            },
            {
              path: 'source',
              element: <RemotePage />,
            },
            {
              path: 'destination',
              element: <RemotePage />,
            },
            {
              path: 'Manual',
              element: <ManualPage menu={menu} />,
              children: [
                {
                  index: true,
                  element: <FAQ />,
                },
                {
                  path: 'directions',
                  element: (
                    <article className="manual-directions-article">
                      <Directions data={jsonData} />
                    </article>
                  ),
                },
              ],
            },
            {
              path: '*',
              element: (
                <section style={{
                  display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh',
                }}
                >
                  <hgroup className="circle-group">
                    <h2>404</h2>
                    <p>Page Not Found!</p>
                  </hgroup>
                </section>
              ),
            },
          ],
        },
      ] : [
        {
          path: '/',
          element: <Login />,
        },
        {
          path: '*',
          element: <Navigate to="/" />,
        },
      ]
    ,
  )

  return (
    <RouterProvider router={router} />
  )
}

export default App
